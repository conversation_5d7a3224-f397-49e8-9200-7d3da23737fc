<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '系统管理员登录',
  },
  access: {
    requireAuth: false,
  },
}
</route>

<template>
  <view class="login-page">
    <!-- 使用自定义导航栏 -->
    <Navbar title="系统管理员登录" :fixed="true" :placeholder="true" @back="goBack" />

    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 登录方式切换 -->
      <LoginTabs v-model="loginType" :tabs="loginTabs" />

      <!-- 登录表单 -->
      <view class="form-content">
        <!-- 密码登录 -->
        <block v-if="loginType === 'password'">
          <LoginInput
            v-model="loginForm.username"
            placeholder="请输入管理员账号"
            :clearable="true"
            :maxlength="50"
          />
          <LoginInput
            v-model="loginForm.password"
            show-password
            placeholder="请输入密码"
            :clearable="true"
            :maxlength="20"
          />
        </block>

        <!-- 短信登录 -->
        <block v-else>
          <LoginInput
            v-model="smsForm.phone"
            type="tel"
            placeholder="请输入手机号"
            :clearable="true"
            :maxlength="11"
          />
          <CodeInput
            v-model="smsForm.code"
            placeholder="请输入验证码"
            :can-send="canSendCode"
            :countdown="countdown"
            @send="sendSmsCode"
          />
        </block>

        <!-- 协议同意 -->
        <AgreementCheckbox
          v-model="agreeTerms"
          user-agreement-text="管理协议"
          @show-agreement="showUserAgreement"
          @show-privacy="showPrivacyPolicy"
        />

        <!-- 登录按钮 -->
        <wd-button
          type="primary"
          size="large"
          :loading="loginLoading"
          :disabled="!canLogin"
          @click="handleLogin"
          custom-class="login-btn"
        >
          {{ loginLoading ? '登录中...' : agreeTerms ? '登录' : '同意协议并登录' }}
        </wd-button>

        <!-- 忘记密码 -->
        <view class="form-footer">
          <text class="link-text center" @click="goToForgotPassword">忘记密码？</text>
        </view>
      </view>

      <!-- 其他登录方式 -->
      <OtherLogin
        :show-sms-login="false"
        @wechat-login="handleWechatLogin"
        @get-phone-number="handleGetPhoneNumber"
      />
    </view>

    <!-- 协议弹窗 -->
    <AgreementPopup
      v-model="showAgreementPopup"
      :title="agreementTitle"
      :content="agreementContent"
    />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import WxUtil from '../../utils/wxUtil'
import PLATFORM from '../../utils/platform'
import { useSystemAdminStore } from '@/store'
import { useMenuPermissionStore } from '@/store'
import { IWechatLoginForm } from '@/api/sys/types'

// 获取系统管理员store
const systemAdminStore = useSystemAdminStore()

// 登录方式
const loginType = ref<'password' | 'sms'>('password')

// 登录标签
const loginTabs = [
  { label: '密码登录', value: 'password' },
  { label: '验证码登录', value: 'sms' },
]

// 表单数据
const loginForm = ref({
  username: '',
  password: '',
})

const smsForm = ref({
  phone: '',
  code: '',
})

// 状态管理
const loginLoading = ref(false)
const wechatLoginLoading = ref(false)
const countdown = ref(0)
let countdownTimer: any = null

// 协议弹窗
const showAgreementPopup = ref(false)
const agreementTitle = ref('')
const agreementContent = ref('')

// 协议同意
const agreeTerms = ref(false)

// 计算属性
const canLogin = computed(() => {
  if (loginType.value === 'password') {
    return loginForm.value.username.trim() && loginForm.value.password.trim()
  } else {
    return smsForm.value.phone.trim() && smsForm.value.code.trim()
  }
})

const canSendCode = computed(() => {
  return /^1[3-9]\d{9}$/.test(smsForm.value.phone)
})

// 发送验证码
const sendSmsCode = async () => {
  if (!canSendCode.value) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none',
    })
    return
  }

  try {
    // TODO: 调用发送验证码API
    console.log('发送验证码到:', smsForm.value.phone)

    // 模拟发送成功
    uni.showToast({
      title: '验证码已发送',
      icon: 'success',
    })

    // 开始倒计时
    countdown.value = 60
    countdownTimer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(countdownTimer)
      }
    }, 1000)
  } catch (error: any) {
    console.error('发送验证码失败:', error)
    uni.showToast({
      title: error.message || '发送失败',
      icon: 'none',
    })
  }
}

// 页面方法
const handleLogin = async () => {
  if (!canLogin.value) {
    uni.showToast({
      title: '请完善登录信息',
      icon: 'none',
    })
    return
  }

  // 如果用户没有同意协议，点击按钮时自动同意
  if (!agreeTerms.value) {
    agreeTerms.value = true
  }

  loginLoading.value = true
  try {
    if (loginType.value === 'password') {
      // 使用真实的登录API
      const result = await systemAdminStore.login({
        username: loginForm.value.username,
        password: loginForm.value.password,
      })

      if (result.success) {
        uni.showToast({
          title: '登录成功',
          icon: 'success',
        })

        // 登录成功后跳转到系统管理首页
        // 这里获取系统用户菜单权限的首元素path  当做默认页面  这里不应写死 系统用户可能没有系统首页仪表盘权限
        const path = useMenuPermissionStore().tabBarList[0].path
        console.log('跳转到系统管理员首页：', path)
        setTimeout(() => {
          uni.redirectTo({
            url: path,
          })
        }, 100)
      } else {
        // 显示具体的错误信息
        throw new Error(result.error || '登录失败')
      }
    } else {
      // TODO: 调用手机号登录API
      console.log('管理员手机号登录信息:', smsForm.value)
      uni.showToast({
        title: '手机号登录功能开发中',
        icon: 'none',
      })
    }
  } catch (error: any) {
    console.error('登录失败:', error)

    // 获取错误信息
    let errorMessage = '登录失败'
    if (error instanceof Error) {
      errorMessage = error.message
    } else if (typeof error === 'string') {
      errorMessage = error
    }

    // 显示错误信息
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000, // 延长显示时间到3秒
    })
  } finally {
    loginLoading.value = false
  }
}

// 微信登录
const handleWechatLogin = async () => {
  // 微信登录需要用户主动同意协议
  if (!agreeTerms.value) {
    uni.showToast({
      title: '请先同意管理协议',
      icon: 'none',
    })
    return
  }

  wechatLoginLoading.value = true
  try {
    console.log('开始微信登录（管理员）')
    let wxLoginCode = ''

    // 微信小程序环境
    if (PLATFORM.isMpWeixin) {
      console.log('微信小程序登录（管理员）')
      const mpLoginResult = await WxUtil.login()
      console.log('获取到微信登录code:', mpLoginResult.code)

      wxLoginCode = mpLoginResult.code
    }
    // APP环境
    else if (PLATFORM.isApp) {
      console.log('APP端微信登录（管理员）')
      const isWXAppInstalled = await new Promise<boolean>((resolve) => {
        if (
          uni.getSystemInfoSync().platform === 'android' ||
          uni.getSystemInfoSync().platform === 'ios'
        ) {
          plus.oauth.getServices(
            (services) => {
              const wxService = services.find((s) => s.id === 'weixin')
              if (wxService) {
                resolve(true)
              } else {
                resolve(false)
              }
            },
            (err) => {
              console.error('获取登录服务列表失败:', err)
              resolve(false)
            },
          )
        } else {
          resolve(false)
        }
      })

      if (!isWXAppInstalled) {
        throw new Error('未安装微信或不支持微信登录')
      }

      const appLoginResult = await WxUtil.login()
      console.log('获取到微信登录code:', appLoginResult.code)

      wxLoginCode = appLoginResult.code
    }
    // H5环境
    else {
      console.log('H5微信登录（管理员）')
      uni.showToast({
        title: 'H5环境暂不支持微信登录',
        icon: 'none',
        duration: 2000,
      })
      wechatLoginLoading.value = false
      return
    }

    // 调用后端API验证管理员身份
    console.log('验证管理员微信身份，code:', wxLoginCode)

    const wechatLoginForm: IWechatLoginForm = {
      code: wxLoginCode,
    }
    // 调用微信登录API
    const result = await systemAdminStore.wechatLogin(wechatLoginForm)

    if (result.success) {
      uni.showToast({
        title: '登录成功',
        icon: 'success',
      })

      // 登录成功后跳转到系统管理首页
      // 这里获取系统用户菜单权限的首元素path作为默认页面
      const path = useMenuPermissionStore().tabBarList[0].path
      console.log('跳转到系统管理员首页：', path)
      setTimeout(() => {
        uni.redirectTo({
          url: path,
        })
      }, 100)
    } else {
      // 显示具体的错误信息
      throw new Error(result.error || '微信登录失败')
    }
  } catch (error: any) {
    console.error('微信登录失败:', error)

    // 获取错误信息
    let errorMessage = '微信登录失败'
    if (error instanceof Error) {
      errorMessage = error.message
    } else if (typeof error === 'string') {
      errorMessage = error
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000,
    })
  } finally {
    wechatLoginLoading.value = false
  }
}

// 手机号快捷登录
const handleGetPhoneNumber = async (e: any) => {
  console.log('微信获取手机号回调（管理员）:', e)

  if (e.detail.errMsg === 'getPhoneNumber:ok') {
    try {
      // TODO: 调用后端API验证管理员身份
      console.log('手机号授权成功，验证管理员身份...')

      uni.showToast({
        title: '正在验证身份...',
        icon: 'loading',
      })

      // 模拟验证
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // 模拟验证结果
      const isAdmin = true

      if (isAdmin) {
        uni.showToast({
          title: '登录成功',
          icon: 'success',
        })

        setTimeout(() => {
          uni.reLaunch({
            url: '/pages-sys/dashboard/index',
          })
        }, 1000)
      } else {
        throw new Error('您不是系统管理员')
      }
    } catch (error: any) {
      console.error('手机号登录失败:', error)
      uni.showToast({
        title: error.message || '登录失败',
        icon: 'none',
      })
    }
  } else {
    uni.showToast({
      title: '需要授权手机号才能登录',
      icon: 'none',
    })
  }
}

// 协议相关
const showUserAgreement = () => {
  agreementTitle.value = '管理协议'
  agreementContent.value = `测试管理协议内容

这是测试环境的管理协议。

1. 测试管理条款一
2. 测试管理条款二
3. 测试管理条款三

仅供测试使用。`
  showAgreementPopup.value = true
}

const showPrivacyPolicy = () => {
  agreementTitle.value = '隐私政策'
  agreementContent.value = `测试隐私政策内容

这是测试环境的隐私政策。

1. 测试隐私条款一
2. 测试隐私条款二
3. 测试隐私条款三

仅供测试使用。`
  showAgreementPopup.value = true
}

// 其他页面跳转
const goToForgotPassword = () => {
  uni.showToast({
    title: '请联系超级管理员重置密码',
    icon: 'none',
  })
}

// 返回按钮
const goBack = () => {
  console.log('系统管理员登录页面 - 点击返回按钮')

  // 尝试返回上一页
  // uni.navigateBack({
  uni.navigateBack({
    fail: () => {
      console.log('无法返回上一页，跳转到普通用户登录页面')
      // 如果无法返回，跳转到普通用户的登录页面，而不是首页
      // 避免跳转到需要认证的页面
      uni.redirectTo({
        url: '/pages/user/login', // 跳转到普通用户登录页面
      })
    },
  })
}

// 清理定时器
onMounted(() => {
  return () => {
    if (countdownTimer) {
      clearInterval(countdownTimer)
    }
  }
})
</script>

<style lang="scss" scoped>
.login-page {
  width: 100%;
  min-height: 100vh;
  background: #ffffff;
  position: relative;
}

/* 内容区域 - 调整顶部内边距，因为现在有固定的导航栏 */
.content-area {
  padding: 24px 24px 40px;
  width: 100%;
  box-sizing: border-box;
}

/* 表单内容 */
.form-content {
  display: flex;
  flex-direction: column;
  padding: 24px 0;
}

/* 登录按钮 */
:deep(.login-btn) {
  margin-top: 20px;
  height: 52px;
  border-radius: 12px;
  background: #ff4757;
  font-size: 16px;
  font-weight: 600;
  width: 100%;
  box-shadow: none !important;
  transition: all 0.3s ease;

  &:hover,
  &:focus {
    box-shadow: none !important;
  }

  &:active:not(.wd-button--disabled) {
    transform: translateY(2px) scale(0.98);
    box-shadow: none !important;
  }

  &.wd-button--disabled {
    background: #ccc;
    box-shadow: none !important;
  }

  &.wd-button--loading {
    background: #ff6b7a;
  }

  &.wd-button {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* 表单底部 */
.form-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 16px;
}

.link-text {
  font-size: 14px;
  color: #007aff;
  text-decoration: none;

  &:active {
    opacity: 0.7;
  }
}
</style>
