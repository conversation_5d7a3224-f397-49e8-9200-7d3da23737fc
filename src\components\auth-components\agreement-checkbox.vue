<template>
  <view class="agreement-section">
    <wd-checkbox
      :modelValue="modelValue"
      @update:modelValue="$emit('update:modelValue', $event)"
      custom-class="agreement-checkbox"
    />
    <view class="agreement-text" @click.stop="$emit('update:modelValue', !modelValue)">
      <text class="normal-text">我已阅读并同意</text>
      <text class="link-text" @click.stop="$emit('show-agreement')">《{{ userAgreementText }}》</text>
      <text class="normal-text">和</text>
      <text class="link-text" @click.stop="$emit('show-privacy')">《隐私政策》</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
interface Props {
  modelValue: boolean
  userAgreementText?: string
}

withDefaults(defineProps<Props>(), {
  userAgreementText: '用户协议'
})

defineEmits(['update:modelValue', 'show-agreement', 'show-privacy'])
</script>

<style lang="scss" scoped>
.agreement-section {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 20px 0;
  margin-bottom: 8px;
}

:deep(.agreement-checkbox) {
  flex-shrink: 0;

  .wd-checkbox__shape {
    width: 20px !important;
    height: 20px !important;
    border-width: 2px !important;
    border-color: #999 !important;
    transition: all 0.3s ease;

    &.is-checked {
      border-color: #007aff !important;
      background-color: #007aff !important;
      animation: checkboxAnimation 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }
  }

  .wd-checkbox__icon {
    color: #fff !important;
  }
}

.agreement-text {
  flex: 1;
  line-height: 1.6;
  user-select: none;
  -webkit-user-select: none;

  &:active {
    opacity: 0.8;
  }
}

.normal-text {
  font-size: 14px;
  color: #666;
  vertical-align: middle;
}

.link-text {
  font-size: 14px;
  color: #007aff;
  text-decoration: none;
  font-weight: 500;
  position: relative;
  display: inline-block;
  vertical-align: middle;
  padding: 2px 0;
  transition: all 0.2s ease;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: #007aff;
    opacity: 0.3;
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }

  &:hover {
    &::after {
      transform: scaleX(1);
    }
  }

  &:active {
    color: #0056b3;
    transform: scale(0.95);

    &::after {
      opacity: 1;
      transform: scaleX(1);
    }
  }
}

@keyframes checkboxAnimation {
  0% {
    transform: scale(0.7) rotate(-45deg);
  }
  50% {
    transform: scale(1.2) rotate(10deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}
</style>
