/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/user/index" |
       "/pages/index/index" |
       "/pages/user/cart" |
       "/pages/user/category" |
       "/pages/user/goods-detail" |
       "/pages/user/login" |
       "/pages/user/merchant-category" |
       "/pages/user/merchant-goods" |
       "/pages/user/merchant-home" |
       "/pages/user/merchant-qualification" |
       "/pages/user/mine" |
       "/pages/user/profile-setup" |
       "/pages/user/search" |
       "/pages-sys/dashboard/index" |
       "/pages-sys/login/login" |
       "/pages-sys/merchant/index" |
       "/pages-sys/mine/mine" |
       "/pages-sys/profile/change-password" |
       "/pages-sys/profile/index" |
       "/pages-sys/profile/wechat-info" |
       "/pages-sys/system/index" |
       "/pages-sys/merchant/merchant/index" |
       "/pages-sys/merchant/merchant/merchant-detail" |
       "/pages-sys/merchant/merchant/merchant-form" |
       "/pages-sys/merchant/merchant-authorized-permission/edit-permission" |
       "/pages-sys/merchant/merchant-authorized-permission/index" |
       "/pages-sys/merchant/merchant-authorized-permission/view-permission" |
       "/pages-sys/merchant/merchant-category/index" |
       "/pages-sys/merchant/merchant-category/merchant-category-form" |
       "/pages-sys/merchant/permission-template/index" |
       "/pages-sys/merchant/permission-template/merchant-permission-display" |
       "/pages-sys/merchant/permission-template/merchant-permission-form" |
       "/pages-sys/merchant/role/index" |
       "/pages-sys/merchant/role/merchant-role-form" |
       "/pages-sys/merchant/role/role-permission-manage" |
       "/pages-sys/merchant/user/index" |
       "/pages-sys/merchant/user/merchant-user-form" |
       "/pages-sys/system/permission/index" |
       "/pages-sys/system/permission/permission-display" |
       "/pages-sys/system/permission/permission-form" |
       "/pages-sys/system/role/index" |
       "/pages-sys/system/role/role-form" |
       "/pages-sys/system/user/index" |
       "/pages-sys/system/user/user-detail" |
       "/pages-sys/system/user/user-form" |
       "/pages-merchants/goods/goods" |
       "/pages-merchants/index/index" |
       "/pages-merchants/login/login" |
       "/pages-merchants/mine/mine" |
       "/pages-merchants/orders/orders" |
       "/pages-merchants/statistics/statistics";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/user/index" | "/pages/user/category" | "/pages/user/cart" | "/pages/user/mine"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
