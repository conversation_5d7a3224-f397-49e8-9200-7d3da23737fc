import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

/**
 * 普通用户基础信息接口
 */
export interface NormalUserInfo {
  id: string // 用户唯一标识
  username: string // 用户名
  nickname?: string // 昵称
  realName?: string // 真实姓名
  avatar: string // 头像URL
  phone: string // 手机号
  email?: string // 邮箱
  gender?: number // 性别：0-未知 1-男 2-女
  birthday?: string // 生日
  status: number // 用户状态：0-禁用 1-启用
  createTime: number // 创建时间戳
  lastLoginTime: number // 最后登录时间戳
  lastLoginIp: string // 最后登录IP
}

/**
 * Token 信息接口
 */
export interface Token {
  token: string
}

// 购物车商品类型
export interface CartItem {
  id: string
  goodsId: string
  title: string
  price: number
  originalPrice?: number
  image: string
  quantity: number
  selected: boolean
  merchantId: string
  merchantName: string
  specs?: string // 规格信息
}

// 收藏商品类型
export interface FavoriteItem {
  id: string
  goodsId: string
  title: string
  price: number
  originalPrice?: number
  image: string
  merchantId: string
  merchantName: string
  addTime: number
}

// 浏览历史类型
export interface BrowseHistory {
  id: string
  goodsId: string
  title: string
  price: number
  image: string
  merchantId: string
  merchantName: string
  browseTime: number
}

// 搜索历史类型
export interface SearchHistory {
  keyword: string
  searchTime: number
}

// 用户位置类型
export interface UserLocation {
  latitude: number
  longitude: number
  address: string
  city: string
  district: string
  province: string
  updateTime: number
}

export const useNormalUserStore = defineStore(
  'normalUser',
  () => {
    // ==================== 用户基础信息 ====================
    const userInfo = ref<NormalUserInfo | null>(null)

    // Token 信息
    const token = ref<Token | null>(null)

    // ==================== 用户管理方法 ====================

    /**
     * 设置用户基础信息
     */
    const setUserInfo = (user: NormalUserInfo) => {
      userInfo.value = user
    }

    /**
     * 设置Token
     */
    const setToken = (tokenData: Token) => {
      token.value = tokenData
    }

    /**
     * 获取Token
     */
    const getToken = computed(() => {
      return token.value
    })

    /**
     * 是否登录
     */
    const isLoggedIn = computed(() => {
      return userInfo.value !== null && token.value !== null
    })

    // ==================== 购物车相关 ====================
    const cartItems = ref<CartItem[]>([])

    // 添加到购物车
    const addToCart = (item: Omit<CartItem, 'id' | 'selected'>) => {
      const existingItem = cartItems.value.find(
        (cartItem) => cartItem.goodsId === item.goodsId && cartItem.specs === item.specs,
      )

      if (existingItem) {
        existingItem.quantity += item.quantity
      } else {
        cartItems.value.push({
          ...item,
          id: `cart_${Date.now()}_${Math.random()}`,
          selected: true,
        })
      }
    }

    // 更新购物车商品数量
    const updateCartQuantity = (id: string, quantity: number) => {
      const item = cartItems.value.find((item) => item.id === id)
      if (item) {
        if (quantity <= 0) {
          removeFromCart(id)
        } else {
          item.quantity = quantity
        }
      }
    }

    // 从购物车移除
    const removeFromCart = (id: string) => {
      const index = cartItems.value.findIndex((item) => item.id === id)
      if (index > -1) {
        cartItems.value.splice(index, 1)
      }
    }

    // 切换商品选中状态
    const toggleCartItemSelected = (id: string) => {
      const item = cartItems.value.find((item) => item.id === id)
      if (item) {
        item.selected = !item.selected
      }
    }

    // 全选/取消全选
    const toggleAllCartItems = (selected: boolean) => {
      cartItems.value.forEach((item) => {
        item.selected = selected
      })
    }

    // 清空购物车
    const clearCart = () => {
      cartItems.value = []
    }

    // 购物车总数量
    const cartTotalCount = computed(() => {
      return cartItems.value.reduce((total, item) => total + item.quantity, 0)
    })

    // 选中商品总数量
    const selectedCartCount = computed(() => {
      return cartItems.value
        .filter((item) => item.selected)
        .reduce((total, item) => total + item.quantity, 0)
    })

    // 选中商品总价
    const selectedCartTotal = computed(() => {
      return cartItems.value
        .filter((item) => item.selected)
        .reduce((total, item) => total + item.price * item.quantity, 0)
    })

    // 是否全选
    const isAllCartSelected = computed(() => {
      return cartItems.value.length > 0 && cartItems.value.every((item) => item.selected)
    })

    // ==================== 收藏相关 ====================
    const favoriteItems = ref<FavoriteItem[]>([])

    // 添加收藏
    const addToFavorites = (item: Omit<FavoriteItem, 'id' | 'addTime'>) => {
      const existingItem = favoriteItems.value.find((fav) => fav.goodsId === item.goodsId)
      if (!existingItem) {
        favoriteItems.value.unshift({
          ...item,
          id: `fav_${Date.now()}_${Math.random()}`,
          addTime: Date.now(),
        })
      }
    }

    // 移除收藏
    const removeFromFavorites = (goodsId: string) => {
      const index = favoriteItems.value.findIndex((item) => item.goodsId === goodsId)
      if (index > -1) {
        favoriteItems.value.splice(index, 1)
      }
    }

    // 检查是否已收藏
    const isFavorited = (goodsId: string) => {
      return favoriteItems.value.some((item) => item.goodsId === goodsId)
    }

    // 切换收藏状态
    const toggleFavorite = (item: Omit<FavoriteItem, 'id' | 'addTime'>) => {
      if (isFavorited(item.goodsId)) {
        removeFromFavorites(item.goodsId)
        return false
      } else {
        addToFavorites(item)
        return true
      }
    }

    // ==================== 浏览历史相关 ====================
    const browseHistory = ref<BrowseHistory[]>([])

    // 添加浏览记录
    const addBrowseHistory = (item: Omit<BrowseHistory, 'id' | 'browseTime'>) => {
      // 移除已存在的记录
      const existingIndex = browseHistory.value.findIndex(
        (history) => history.goodsId === item.goodsId,
      )
      if (existingIndex > -1) {
        browseHistory.value.splice(existingIndex, 1)
      }

      // 添加到开头
      browseHistory.value.unshift({
        ...item,
        id: `browse_${Date.now()}_${Math.random()}`,
        browseTime: Date.now(),
      })

      // 限制历史记录数量（最多100条）
      if (browseHistory.value.length > 100) {
        browseHistory.value = browseHistory.value.slice(0, 100)
      }
    }

    // 清空浏览历史
    const clearBrowseHistory = () => {
      browseHistory.value = []
    }

    // ==================== 搜索历史相关 ====================
    const searchHistory = ref<SearchHistory[]>([])

    // 添加搜索记录
    const addSearchHistory = (keyword: string) => {
      if (!keyword.trim()) return

      // 移除已存在的记录
      const existingIndex = searchHistory.value.findIndex((history) => history.keyword === keyword)
      if (existingIndex > -1) {
        searchHistory.value.splice(existingIndex, 1)
      }

      // 添加到开头
      searchHistory.value.unshift({
        keyword,
        searchTime: Date.now(),
      })

      // 限制搜索历史数量（最多20条）
      if (searchHistory.value.length > 20) {
        searchHistory.value = searchHistory.value.slice(0, 20)
      }
    }

    // 清空搜索历史
    const clearSearchHistory = () => {
      searchHistory.value = []
    }

    // ==================== 用户位置相关 ====================
    const currentLocation = ref<UserLocation | null>(null)

    // 更新用户位置
    const updateLocation = (location: Omit<UserLocation, 'updateTime'>) => {
      currentLocation.value = {
        ...location,
        updateTime: Date.now(),
      }
    }

    // 获取用户位置
    const getUserLocation = (): Promise<UserLocation> => {
      return new Promise((resolve, reject) => {
        uni.getLocation({
          type: 'gcj02',
          success: (res) => {
            // 获取到经纬度后，需要调用逆地理编码获取地址信息
            // 这里先返回基本的位置信息，实际项目中需要调用地图API获取详细地址
            const location: UserLocation = {
              latitude: res.latitude,
              longitude: res.longitude,
              address: '获取地址中...',
              city: '',
              district: '',
              province: '',
              updateTime: Date.now(),
            }

            // 更新store中的位置
            currentLocation.value = location
            resolve(location)
          },
          fail: (err) => {
            console.error('获取位置失败:', err)
            reject(err)
          },
        })
      })
    }

    // 清空位置信息
    const clearLocation = () => {
      currentLocation.value = null
    }

    // 检查位置是否过期（超过30分钟）
    const isLocationExpired = (): boolean => {
      if (!currentLocation.value) return true
      const now = Date.now()
      const thirtyMinutes = 30 * 60 * 1000
      return now - currentLocation.value.updateTime > thirtyMinutes
    }

    // 获取当前有效位置（如果过期则重新获取）
    const getCurrentValidLocation = async (): Promise<UserLocation> => {
      if (!currentLocation.value || isLocationExpired()) {
        return await getUserLocation()
      }
      return currentLocation.value
    }

    // ==================== 用户偏好设置 ====================
    const preferences = ref({
      // 是否开启消息推送
      enableNotification: true,
      // 是否开启位置服务
      enableLocation: true,
    })

    // 更新偏好设置
    const updatePreferences = (newPreferences: Partial<typeof preferences.value>) => {
      preferences.value = { ...preferences.value, ...newPreferences }
    }
    const logout = () => {}

    // ==================== 清空所有数据 ====================
    const clearAllData = () => {
      userInfo.value = null
      token.value = null
      cartItems.value = []
      favoriteItems.value = []
      browseHistory.value = []
      searchHistory.value = []
      currentLocation.value = null
      preferences.value = {
        enableNotification: true,
        enableLocation: true,
      }
    }

    return {
      // 用户基础信息
      userInfo,
      token,

      // 用户管理方法
      setUserInfo,
      setToken,
      getToken,
      isLoggedIn,
      logout,

      // 购物车
      cartItems,
      addToCart,
      updateCartQuantity,
      removeFromCart,
      toggleCartItemSelected,
      toggleAllCartItems,
      clearCart,
      cartTotalCount,
      selectedCartCount,
      selectedCartTotal,
      isAllCartSelected,

      // 收藏
      favoriteItems,
      addToFavorites,
      removeFromFavorites,
      isFavorited,
      toggleFavorite,

      // 浏览历史
      browseHistory,
      addBrowseHistory,
      clearBrowseHistory,

      // 搜索历史
      searchHistory,
      addSearchHistory,
      clearSearchHistory,

      // 用户位置
      currentLocation,
      updateLocation,
      getUserLocation,
      clearLocation,
      isLocationExpired,
      getCurrentValidLocation,

      // 偏好设置
      preferences,
      updatePreferences,

      // 清空所有数据
      clearAllData,
    }
  },
  {
    persist: true, // 启用数据持久化
  },
)
