<template>
  <view class="login-input-wrapper">
    <wd-input
      :modelValue="modelValue"
      @update:modelValue="$emit('update:modelValue', $event)"
      :type="type"
      :show-password="showPassword"
      :placeholder="placeholder"
      :clearable="clearable"
      :maxlength="maxlength"
      :disabled="disabled"
      custom-class="login-input"
    />
  </view>
</template>

<script lang="ts" setup>
interface Props {
  modelValue: string
  type?: 'text' | 'number' | 'idcard' | 'digit' | 'tel' | 'safe-password' | 'nickname'
  showPassword?: boolean
  placeholder?: string
  clearable?: boolean
  maxlength?: number
  disabled?: boolean
}

defineProps<Props>()
defineEmits(['update:modelValue'])
</script>

<style lang="scss" scoped>
.login-input-wrapper {
  margin-bottom: 24px;
  
  :deep(.login-input) {
    border: none !important;
    border-radius: 0 !important;
    background: transparent !important;
    box-shadow: none !important;
    padding: 8px 0 !important;
    border-bottom: 1px solid #e1e5e9 !important;
    position: relative;

    .wd-input__inner {
      height: 48px;
      font-size: 16px;
      padding: 0 4px;
      background: transparent !important;
      border: none !important;
      box-shadow: none !important;
    }
  }
}
</style> 