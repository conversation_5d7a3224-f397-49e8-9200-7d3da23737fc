<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '主页',
  },
}
</route>

<template>
  <view class="home-container" :style="{ paddingTop: safeAreaInsets?.top + 'px' }">
    <!-- 页面内容 -->
    <view class="content">
      <text>商户用户 - 主页内容</text>
    </view>
    <!-- 底部导航栏 -->
    <TabBar current-page="index"/>
  </view>
</template>

<script lang="ts" setup>
import { useGlobalSafeArea } from '@/hooks/useSafeArea'

defineOptions({
  name: 'MerchantIndex',
})

// 使用全局安全区域 hook
const { safeAreaInsetsTop } = useGlobalSafeArea()
// 为了兼容模板中的使用方式，创建 safeAreaInsets 对象
const safeAreaInsets = computed(() => ({
  top: safeAreaInsetsTop.value
}))

// 用户角色，这里可以从 store 或其他地方获取
const userRole = ref<'normal' | 'merchant' | 'admin'>('merchant')
</script>

<style lang="scss" scoped>
.home-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
