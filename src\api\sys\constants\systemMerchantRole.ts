/**
 * 系统商户角色管理相关常量
 */

/**
 * 分页查询默认参数
 */
export const SysMerchantRolePageConstants = {
  // 默认页码
  DEFAULT_PAGE: 1,
  // 默认每页大小
  DEFAULT_PAGE_SIZE: 10,
  // 最大每页大小
  MAX_PAGE_SIZE: 100,
  // 最小每页大小
  MIN_PAGE_SIZE: 1,
} as const

/**
 * 商户角色类型枚举及映射
 */
export enum SysMerchantRoleType {
  // 管理员角色
  ADMIN = 1,
  // 自定义角色
  CUSTOM = 2,
}

export const SysMerchantRoleTypeMap = {
  [SysMerchantRoleType.ADMIN]: '管理员角色',
  [SysMerchantRoleType.CUSTOM]: '自定义角色',
} as const

/**
 * 商户角色数据范围枚举及映射
 */
export enum SysMerchantRoleDataScope {
  // 商户全部数据
  ALL = 1,
  // 个人数据
  PERSONAL = 2,
}

export const SysMerchantRoleDataScopeMap = {
  [SysMerchantRoleDataScope.ALL]: '商户全部数据',
  [SysMerchantRoleDataScope.PERSONAL]: '个人数据',
} as const

/**
 * 商户角色状态枚举及映射
 */
export enum SysMerchantRoleStatus {
  // 禁用
  DISABLED = 0,
  // 启用
  ENABLED = 1,
}

export const SysMerchantRoleStatusMap = {
  [SysMerchantRoleStatus.DISABLED]: '禁用',
  [SysMerchantRoleStatus.ENABLED]: '启用',
} as const

/**
 * 商户角色验证规则常量
 */
export const SysMerchantRoleValidation = {
  // 角色名称
  ROLE_NAME_MIN_LENGTH: 2,
  ROLE_NAME_MAX_LENGTH: 30,
  ROLE_NAME_PATTERN: /^[\u4e00-\u9fa5a-zA-Z0-9_\s]{2,30}$/,

  // 角色编码
  ROLE_CODE_MIN_LENGTH: 3,
  ROLE_CODE_MAX_LENGTH: 50,
  ROLE_CODE_PATTERN: /^[A-Z_][A-Z0-9_]{2,49}$/,

  // 角色描述
  ROLE_DESC_MAX_LENGTH: 100,

  // 备注
  REMARK_MAX_LENGTH: 200,
} as const 