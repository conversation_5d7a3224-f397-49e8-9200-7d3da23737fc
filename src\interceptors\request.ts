/* eslint-disable no-param-reassign */
import qs from 'qs'
import { useNormalUserStore } from '@/store/normalUser'
import { useSystemAdminStore } from '@/store/systemAdmin'
import { platform } from '@/utils/platform'
import { getEnvBaseUrl } from '@/utils'
import { getAuthAdapter } from '@/utils/auth/factory'
import { userTypeEnum } from '@/store/globalRole'

// 声明全局变量类型
declare const __VITE_APP_PROXY__: string

export type CustomRequestOptions = UniApp.RequestOptions & {
  query?: Record<string, any>
  /** 出错时是否隐藏错误提示 */
  hideErrorToast?: boolean
} & IUniUploadFileOptions // 添加uni.uploadFile参数类型

// 请求基准地址
const baseUrl = getEnvBaseUrl()

/**
 * Token 添加策略配置
 */
const TOKEN_STRATEGIES = {
  [userTypeEnum.system]: (token: string) => ({ Authorization: `Bearer ${token}` }),
  [userTypeEnum.merchants]: (token: string) => ({ 'Bearer': token }),
  [userTypeEnum.user]: (token: string) => ({ Authorization: `Bearer ${token}` }),
}

/**
 * 添加认证头的方法（同步版本）
 */
function addAuthHeaders(options: CustomRequestOptions) {
  try {
    // 获取当前用户类型适配器
    const adapter = getAuthAdapter()
    const defaultHeaders = adapter.getDefaultHeaders()
    const token = adapter.getToken()
    // 合并默认请求头
    options.header = {
      ...defaultHeaders,
      ...options.header,
    }
    // options.forceCellularNetwork = true

    // 添加 Token（使用策略模式）
    if (token) {
      const userType = adapter.getUserType()
      const tokenStrategy = TOKEN_STRATEGIES[userType]

      if (tokenStrategy) {
        const authHeaders = tokenStrategy(token)
        options.header = {
          ...options.header,
          ...authHeaders,
        }
      }
    }

    // 添加用户类型标识到日志
    const userTypeStr = adapter.getUserType().toString().toUpperCase()
    console.log(`[${userTypeStr}-INTERCEPTOR] 请求准备:`, options.url)

  } catch (error) {
    console.error('添加认证头失败:', error)
    // 如果适配器获取失败，fallback 到原来的逻辑
    const userStore = useNormalUserStore()
    const { token } = userStore.userInfo as unknown as IUserInfo
    if (token) {
      options.header = {
        ...options.header,
        Authorization: `Bearer ${token}`,
      }
    }
  }
}

/**
 * 处理 URL 路径
 */
function processUrl(options: CustomRequestOptions) {
  // 接口请求支持通过 query 参数配置 queryString
  if (options.query) {
    const queryStr = qs.stringify(options.query)
    if (options.url.includes('?')) {
      options.url += `&${queryStr}`
    } else {
      options.url += `?${queryStr}`
    }
  }

  console.log('processUrl', options)

  // 非 http 开头需拼接地址
  if (!options.url.startsWith('http')) {
    // #ifdef H5
    if (JSON.parse(__VITE_APP_PROXY__)) {
      // 自动拼接代理前缀
      options.url = import.meta.env.VITE_APP_PROXY_PREFIX + options.url
      console.log('H5', options)
    } else {
      options.url = baseUrl + options.url
      console.log('else H5', options)
    }
    // #endif
    // 非H5正常拼接
    // #ifndef H5
    options.url = baseUrl + options.url
    console.log('非H5正常拼接', options)

    // #endif
  }
}

// 拦截器配置
const httpInterceptor = {
  // 拦截前触发（同步方法）
  invoke(options: CustomRequestOptions) {
    try {
      // 1. 处理 URL 和查询参数
      processUrl(options)

      // 2. 设置请求超时
      options.timeout = 10000 // 10s

      // 3. 添加基础请求头
      options.header = {
        platform, // 与 uniapp 定义的平台一致，告诉后台来源
        ...options.header,
      }

      // 4. 添加用户类型特定的认证头（同步调用）
      addAuthHeaders(options)
    } catch (error) {
      console.error('请求拦截器处理失败:', error)
      // 确保即使拦截器出错，请求也能继续
    }
  },
}

export const requestInterceptor = {
  install() {
    // 拦截 request 请求
    uni.addInterceptor('request', httpInterceptor)
    // 拦截 uploadFile 文件上传
    uni.addInterceptor('uploadFile', httpInterceptor)
  },
}
