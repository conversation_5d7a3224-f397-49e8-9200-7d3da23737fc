import { http } from '@/utils/http'
import type {
  ISysRolePageRequest,
  ISysRoleListResponse,
  ISysRoleSimpleResponse,
  ISysRoleDetailResponse,
  ISysRoleCreateRequest,
  ISysRoleUpdateRequest,
  ISysRolePermissionRequest,
  ISysRoleBatchDeleteRequest,
  IUserRoleInfo,
} from './types'

/**
 * 分页查询角色列表
 * @param params 查询参数
 * @returns 返回分页角色列表
 */
export const getSystemRolesApi = (params?: ISysRolePageRequest) => {
  return http.get<IPageData<ISysRoleListResponse>>('/system/roles', params)
}

/**
 * 创建角色
 * @param createRequest 创建请求参数
 * @returns 返回创建结果
 */
export const createSystemRoleApi = (createRequest: ISysRoleCreateRequest) => {
  return http.post<string>('/system/roles', createRequest)
}

/**
 * 获取角色简单信息
 * @returns 返回所有角色的简单信息
 */
export const getSystemRoleSimpleApi = () => {
  return http.get<ISysRoleSimpleResponse[]>('/system/roles/simple')
}

/**
 * 查询角色详情
 * @param roleId 角色ID
 * @returns 返回角色详情信息
 */
export const getSystemRoleDetailApi = (roleId: string) => {
  return http.get<ISysRoleDetailResponse>(`/system/roles/${roleId}`)
}

/**
 * 更新角色
 * @param roleId 角色ID
 * @param updateRequest 更新请求参数
 * @returns 返回更新结果
 */
export const updateSystemRoleApi = (roleId: string, updateRequest: ISysRoleUpdateRequest) => {
  return http.put<string>(`/system/roles/${roleId}`, updateRequest)
}

/**
 * 删除角色
 * @param roleId 角色ID
 * @returns 返回删除结果
 */
export const deleteSystemRoleApi = (roleId: string) => {
  return http.delete<string>(`/system/roles/${roleId}`)
}

/**
 * 角色权限分配
 * @param roleId 角色ID
 * @param permissionRequest 权限分配请求
 * @returns 返回分配结果
 */
export const assignRolePermissionsApi = (
  roleId: string,
  permissionRequest: ISysRolePermissionRequest,
) => {
  return http.put<string>(`/system/roles/${roleId}/permissions`, permissionRequest)
}

/**
 * 批量删除角色
 * @param batchDeleteRequest 批量删除请求
 * @returns 返回批量删除结果
 */
export const batchDeleteSystemRolesApi = (batchDeleteRequest: ISysRoleBatchDeleteRequest) => {
  return http.post<string>('/system/roles/batch-delete', batchDeleteRequest)
}

// 同时导出接口类型，方便外部使用
export type {
  ISysRolePageRequest,
  ISysRoleListResponse,
  ISysRoleSimpleResponse,
  ISysRoleDetailResponse,
  ISysRoleCreateRequest,
  ISysRoleUpdateRequest,
  ISysRolePermissionRequest,
  ISysRoleBatchDeleteRequest,
  IUserRoleInfo,
}
