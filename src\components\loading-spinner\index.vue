<template>
  <view :class="containerClass">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{ loadingText }}</text>
  </view>
</template>

<script lang="ts" setup>

interface Props {
  loadingText?: string
  containerClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  loadingText: '正在加载商品...',
  containerClass: 'loading-container'
})
</script>

<style lang="scss" scoped>
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}
</style>
