// 全局要用的类型放到这里

declare global {
  type IResData<T> = {
    code: number
    message: string
    data: T
  }

  /**
   * 分页请求参数
   */
  interface IPageParams {
    // 当前页码，从1开始
    page: number
    // 每页数量
    pageSize: number
  }

  /**
   * 分页响应数据
   */
  interface IPageData<T> {
    // 数据列表
    items: T[]
    // 总数量
    total: number
    // 当前页码
    page: number
    // 每页数量
    page_size: number
    // 总页数
    total_pages: number
  }



  // uni.uploadFile文件上传参数
  type IUniUploadFileOptions = {
    file?: File
    files?: UniApp.UploadFileOptionFiles[]
    filePath?: string
    name?: string
    formData?: any
  }

  type IUserInfo = {
    nickname?: string
    avatar?: string
    /** 微信的 openid，非微信没有这个字段 */
    openid?: string
    token?: string
  }
}

export { } // 防止模块污染
