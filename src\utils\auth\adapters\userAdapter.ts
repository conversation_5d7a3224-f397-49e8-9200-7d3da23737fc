import { useNormalUserStore } from '@/store/normalUser'
import { userTypeEnum } from '@/store/globalRole'

/**
 * 普通用户适配器
 * 使用普通 JWT 机制，无 Token 刷新
 */
export class UserAdapter {
  private store = useNormalUserStore()

  /**
   * 获取访问令牌
   */
  getToken(): string | null {
    const userInfo = this.store.userInfo as any
    return userInfo?.token || null
  }

  /**
   * 获取用户类型标识
   */
  getUserType(): userTypeEnum {
    return userTypeEnum.user
  }


  /**
   * 获取默认请求头
   */
  getDefaultHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json'
    }
  }

  /**
   * 检查响应头（普通用户不支持刷新）
   */
  async handleResponseHeaders(headers: Record<string, string>): Promise<boolean> {
    // 普通用户使用普通 JWT，不支持刷新
    // 直接返回 true，让正常的错误处理流程处理过期问题
    return true
  }

  /**
   * 处理 401 未授权错误
   */
  async handleUnauthorized(): Promise<boolean> {
    console.log('普通用户 JWT 已过期，需要重新登录')
    this.handleLogout()
    return false // 普通用户没有刷新机制，始终返回false
  }

  /**
   * 处理 403 权限不足错误
   */
  handleForbidden(): void {
    uni.showToast({
      icon: 'none',
      title: '用户权限不足',
      duration: 2000
    })
  }

  /**
   * 处理登出逻辑
   */
  handleLogout(): void {
    // 使用正确的 logout 方法
    this.store.logout()

    uni.reLaunch({
      url: '/pages/login/login',
      success: () => {
        uni.showToast({
          icon: 'none',
          title: 'JWT 已过期，请重新登录',
          duration: 2000
        })
      }
    })
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn(): boolean {
    const userInfo = this.store.userInfo as any
    return !!(userInfo?.token)
  }

  /**
   * 获取用户信息
   */
  getUserInfo() {
    return this.store.userInfo
  }

  /**
   * 获取用户 ID
   */
  getUserId(): string | number | null {
    const userInfo = this.store.userInfo as any
    return userInfo?.id || null
  }

  /**
   * 获取用户名
   */
  getUsername(): string | null {
    const userInfo = this.store.userInfo as any
    return userInfo?.username || null
  }
}
