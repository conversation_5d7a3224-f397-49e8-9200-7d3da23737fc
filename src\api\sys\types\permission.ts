/**
 * 权限菜单分页查询请求参数
 */
export interface IPermissionPageRequest {
  // 菜单名称
  menu_name?: string | null
  // 权限标识
  perms?: string | null
  // 菜单类型（1目录 2菜单 3按钮）
  menu_type?: number | null
  // 状态（0正常 1停用）
  status?: number | null
  // 当前页码
  page?: number | null
  // 每页大小
  page_size?: number | null
  // 创建开始时间
  created_start?: string | null
  // 创建结束时间
  created_end?: string | null
}

/**
 * 权限菜单列表响应
 */
export interface IPermissionListResponse {
  // 权限ID
  id: string
  // 菜单名称
  menu_name: string
  // 父级ID
  parent_id?: string | null
  // 显示顺序
  order_num?: number | null
  // 路由地址
  path?: string | null
  // 组件路径
  component?: string | null
  // 菜单类型（1目录 2菜单 3按钮）
  menu_type?: number | null
  // 菜单状态（0正常 1停用）
  status?: number | null
  // 权限标识
  perms?: string | null
  // 菜单图标
  icon?: string | null
  // 创建时间
  created_date: string
  // 更新时间
  updated_date: string
}

/**
 * 权限菜单详情响应
 */
export interface IPermissionDetailResponse {
  // 权限ID
  id: string
  // 菜单名称
  menu_name: string
  // 父级ID
  parent_id?: string | null
  // 父级菜单名称（如果有）
  parent_name?: string | null
  // 显示顺序
  order_num?: number | null
  // 路由地址
  path?: string | null
  // 组件路径
  component?: string | null
  // 路由参数
  query?: string | null
  // 是否为外链（0是 1否）
  is_frame?: number | null
  // 是否缓存（0缓存 1不缓存）
  is_cache?: number | null
  // 菜单类型（1目录 2菜单 3按钮）
  menu_type?: number | null
  // 显示状态（0显示 1隐藏）
  visible?: number | null
  // 菜单状态（0正常 1停用）
  status?: number | null
  // 权限标识
  perms?: string | null
  // 菜单图标
  icon?: string | null
  // 创建时间
  created_date: string
  // 更新时间
  updated_date: string
  // 创建人ID
  created_by?: string | null
  // 更新人ID
  updated_by?: string | null
  // 备注
  remark?: string | null
  // 子菜单列表
  children?: IPermissionTreeNode[] | null
}

/**
 * 权限菜单树节点
 */
export interface IPermissionTreeNode {
  // 权限ID
  id: string
  // 菜单名称
  menu_name: string
  // 父级ID
  parent_id?: string | null
  // 显示顺序
  order_num?: number | null
  // 路由地址
  path?: string | null
  // 组件路径
  component?: string | null
  // 菜单类型（1目录 2菜单 3按钮）
  menu_type?: number | null
  // 菜单状态（0正常 1停用）
  status?: number | null
  // 权限标识
  perms?: string | null
  // 菜单图标
  icon?: string | null
  // 子菜单
  children?: IPermissionTreeNode[] | null
}

/**
 * 权限菜单选择项（用于下拉选择）
 */
export interface IPermissionSelectItem {
  // 权限ID
  id: string
  // 菜单名称
  menu_name: string
  // 父级ID
  parent_id?: string | null
  // 显示顺序
  order_num?: number | null
  // 菜单类型（1目录 2菜单 3按钮）
  menu_type?: number | null
}

/**
 * 权限菜单创建请求
 */
export interface IPermissionCreateRequest {
  // 菜单名称
  menu_name: string
  // 父级ID
  parent_id?: string | null
  // 显示顺序
  order_num?: number | null
  // 路由地址
  path?: string | null
  // 组件路径
  component?: string | null
  // 路由参数
  query?: string | null
  // 是否为外链（0是 1否）
  is_frame?: number | null
  // 是否缓存（0缓存 1不缓存）
  is_cache?: number | null
  // 菜单类型（1目录 2菜单 3按钮）
  menu_type?: number | null
  // 显示状态（0显示 1隐藏）
  visible?: number | null
  // 菜单状态（0正常 1停用）
  status?: number | null
  // 权限标识
  perms?: string | null
  // 菜单图标
  icon?: string | null
  // 备注
  remark?: string | null
}

/**
 * 权限菜单更新请求
 */
export interface IPermissionUpdateRequest {
  // 菜单名称
  menu_name: string
  // 父级ID
  parent_id?: string | null
  // 显示顺序
  order_num?: number | null
  // 路由地址
  path?: string | null
  // 组件路径
  component?: string | null
  // 路由参数
  query?: string | null
  // 是否为外链（0是 1否）
  is_frame?: number | null
  // 是否缓存（0缓存 1不缓存）
  is_cache?: number | null
  // 菜单类型（1目录 2菜单 3按钮）
  menu_type?: number | null
  // 显示状态（0显示 1隐藏）
  visible?: number | null
  // 菜单状态（0正常 1停用）
  status?: number | null
  // 权限标识
  perms?: string | null
  // 菜单图标
  icon?: string | null
  // 备注
  remark?: string | null
}

/**
 * 权限菜单状态切换请求
 */
export interface IPermissionStatusRequest {
  // 状态（0正常 1停用）
  status: number
}

/**
 * 权限菜单批量删除请求
 */
export interface IPermissionBatchDeleteRequest {
  // 权限ID列表
  permission_ids: string[]
}

/**
 * 路由元信息
 */
export interface IRouterMeta {
  // 标题
  title: string
  // 图标
  icon?: string | null
  // 是否缓存
  no_cache: boolean
  // 链接地址
  link?: string | null
}

/**
 * 路由菜单项（用于前端生成动态路由）
 */
export interface IRouterVo {
  // 路由名称
  name?: string | null
  // 路由地址
  path: string
  // 是否隐藏
  hidden: boolean
  // 重定向地址
  redirect?: string | null
  // 组件
  component?: string | null
  // 是否总是显示
  always_show?: boolean | null
  // 权限标识
  perms?: string | null
  // 菜单类型（1目录 2菜单 3按钮）
  menu_type?: number | null
  // 路由元信息
  meta: IRouterMeta
  // 子路由
  children?: IRouterVo[] | null
} 