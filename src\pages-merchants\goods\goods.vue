<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '商品管理',
  },
}
</route>

<template>
  <view class="goods-container" :style="{ paddingTop: safeAreaInsets?.top + 'px' }">
    <!-- 页面内容 -->
    <view class="content">
      <text>商户用户 - 商品管理页面内容</text>
    </view>
    <!-- 底部导航栏 -->
    <TabBar current-page="goods"/>
  </view>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'MerchantGoods',
})

// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()

</script>

<style lang="scss" scoped>
.goods-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
