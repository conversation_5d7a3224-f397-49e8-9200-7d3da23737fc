import { http } from '@/utils/http'
import type {
  ISysMerchantUserPageRequest,
  ISysMerchantUserListResponse,
  ISysMerchantUserDetailResponse,
  ISysMerchantUserCreateRequest,
  ISysMerchantUserUpdateRequest,
  ISysMerchantUserStatusRequest,
  ISysMerchantUserResetPasswordRequest,
  ISysMerchantUserBatchDeleteRequest,
  ISysMerchantUserCheckUsernameRequest,
  ISysMerchantUserCheckEmailRequest,
  ISysMerchantUserCheckPhoneRequest,
  ISysMerchantUserCheckRealNameRequest,
  ISysMerchantUserRoleAssignRequest,
  ISysMerchantUserBasicResponse,
  ISysMerchantUserSelectItem,
  ISysMerchantUserStatsResponse,
} from './types'

/**
 * 分页查询商户用户列表
 * @param params 查询参数
 * @returns 返回分页商户用户列表
 */
export const getSystemMerchantUsersApi = (params?: ISysMerchantUserPageRequest) => {
  return http.get<IPageData<ISysMerchantUserListResponse>>('/system/merchant-users', params)
}

/**
 * 查询商户用户详情
 * @param userId 商户用户ID
 * @returns 返回商户用户详情信息
 */
export const getSystemMerchantUserDetailApi = (userId: string) => {
  return http.get<ISysMerchantUserDetailResponse>(`/system/merchant-users/${userId}`)
}

/**
 * 创建新商户用户
 * @param createRequest 创建请求参数
 * @returns 返回创建结果
 */
export const createSystemMerchantUserApi = (createRequest: ISysMerchantUserCreateRequest) => {
  return http.post<string>('/system/merchant-users', createRequest)
}

/**
 * 更新商户用户信息
 * @param userId 商户用户ID
 * @param updateRequest 更新请求参数
 * @returns 返回更新结果
 */
export const updateSystemMerchantUserApi = (
  userId: string,
  updateRequest: ISysMerchantUserUpdateRequest,
) => {
  return http.put<string>(`/system/merchant-users/${userId}`, updateRequest)
}

/**
 * 删除商户用户
 * @param userId 商户用户ID
 * @returns 返回删除结果
 */
export const deleteSystemMerchantUserApi = (userId: string) => {
  return http.delete<string>(`/system/merchant-users/${userId}`)
}

/**
 * 批量删除商户用户
 * @param batchDeleteRequest 批量删除请求
 * @returns 返回批量删除结果
 */
export const batchDeleteSystemMerchantUsersApi = (
  batchDeleteRequest: ISysMerchantUserBatchDeleteRequest,
) => {
  return http.delete<string>('/system/merchant-users/batch', batchDeleteRequest)
}

/**
 * 切换商户用户状态
 * @param userId 商户用户ID
 * @param statusRequest 状态切换请求
 * @returns 返回切换结果
 */
export const changeMerchantUserStatusApi = (
  userId: string,
  statusRequest: ISysMerchantUserStatusRequest,
) => {
  return http.put<string>(`/system/merchant-users/${userId}/status`, statusRequest)
}

/**
 * 重置商户用户密码
 * @param userId 商户用户ID
 * @param resetPasswordRequest 重置密码请求
 * @returns 返回重置结果
 */
export const resetMerchantUserPasswordApi = (
  userId: string,
  resetPasswordRequest: ISysMerchantUserResetPasswordRequest,
) => {
  return http.put<string>(`/system/merchant-users/${userId}/reset-password`, resetPasswordRequest)
}

/**
 * 为商户用户分配角色
 * @param userId 商户用户ID
 * @param assignRequest 角色分配请求
 * @returns 返回分配结果
 */
export const assignRolesToMerchantUserApi = (
  userId: string,
  assignRequest: ISysMerchantUserRoleAssignRequest,
) => {
  return http.post<string>(`/system/merchant-users/${userId}/assign-roles`, assignRequest)
}

/**
 * 检查用户名是否存在
 * @param params 检查参数
 * @returns 返回是否存在
 */
export const checkMerchantUsernameExistsApi = (params: ISysMerchantUserCheckUsernameRequest) => {
  return http.get<boolean>('/system/merchant-users/check-username', params)
}

/**
 * 检查邮箱是否存在
 * @param params 检查参数
 * @returns 返回是否存在
 */
export const checkMerchantEmailExistsApi = (params: ISysMerchantUserCheckEmailRequest) => {
  return http.get<boolean>('/system/merchant-users/check-email', params)
}

/**
 * 检查手机号是否存在
 * @param params 检查参数
 * @returns 返回是否存在
 */
export const checkMerchantPhoneExistsApi = (params: ISysMerchantUserCheckPhoneRequest) => {
  return http.get<boolean>('/system/merchant-users/check-phone', params)
}

/**
 * 检查真实姓名是否存在
 * @param params 检查参数
 * @returns 返回是否存在
 */
export const checkMerchantRealNameExistsApi = (params: ISysMerchantUserCheckRealNameRequest) => {
  return http.get<boolean>('/system/merchant-users/check-real-name', params)
}

/**
 * 获取商户用户基础信息列表
 * @returns 返回商户用户基础信息列表
 */
export const getMerchantUserBasicListApi = () => {
  return http.get<ISysMerchantUserBasicResponse[]>('/system/merchant-users/basic-list')
}

/**
 * 获取商户用户选择项
 * @returns 返回用于下拉选择的商户用户列表
 */
export const getMerchantUserSelectItemsApi = () => {
  return http.get<ISysMerchantUserSelectItem[]>('/system/merchant-users/select-items')
}

/**
 * 获取商户用户统计信息
 * @returns 返回商户用户统计数据
 */
export const getMerchantUserStatsApi = () => {
  return http.get<ISysMerchantUserStatsResponse>('/system/merchant-users/stats')
}

// 同时导出接口类型，方便外部使用
export type {
  ISysMerchantUserPageRequest,
  ISysMerchantUserListResponse,
  ISysMerchantUserDetailResponse,
  ISysMerchantUserCreateRequest,
  ISysMerchantUserUpdateRequest,
  ISysMerchantUserStatusRequest,
  ISysMerchantUserResetPasswordRequest,
  ISysMerchantUserBatchDeleteRequest,
  ISysMerchantUserCheckUsernameRequest,
  ISysMerchantUserCheckEmailRequest,
  ISysMerchantUserCheckPhoneRequest,
  ISysMerchantUserCheckRealNameRequest,
  ISysMerchantUserRoleAssignRequest,
  ISysMerchantUserBasicResponse,
  ISysMerchantUserSelectItem,
  ISysMerchantUserStatsResponse,
} 