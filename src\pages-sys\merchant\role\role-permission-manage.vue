<route lang="json5">
    {
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '角色权限管理',
      },
      access: {
        requireAuth: true,
      },
    }
</route>

<template>
  <view class="role-permission-manage-container">
    <!-- 顶部导航栏 -->
    <wd-navbar
      title="角色权限管理"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <!-- Tab栏 -->
      <wd-tabs v-model="activeTab">
        <!-- 查看权限Tab -->
        <wd-tab title="查看权限" name="view" class="tab-panel">
          <view v-if="loadingView" class="loading-state">
            <wd-loading />
            <text class="loading-text">加载权限数据中...</text>
          </view>
          <view v-else-if="viewPermissionTree.length === 0" class="empty-state">
            <wd-status-tip type="content" tip="未给商户授权权限，该角色暂无权限" />
          </view>
          <view v-else class="permission-container">
            <!-- 操作栏 -->
            <view class="permission-header">
              <view class="permission-actions">
                <wd-button size="small" @click="expandAllView">全部展开</wd-button>
                <wd-button size="small" @click="collapseAllView">全部收起</wd-button>
                <wd-button type="info" size="small" @click="refreshViewData">刷新</wd-button>
              </view>
            </view>
            <!-- 权限树 -->
            <view class="permission-tree">
              <view v-for="node in viewPermissionTree" :key="node.id" class="permission-node-container level-1">
                <view class="permission-node">
                  <view class="permission-node-info">
                    <!-- 展开/收起图标 -->
                    <view
                      class="expand-icon"
                      v-if="node.children && node.children.length > 0"
                      @click="toggleExpandView(node.id)"
                    >
                      <wd-icon
                        :name="expandedViewIds.includes(node.id) ? 'arrow-down' : 'arrow-right'"
                        size="14"
                        color="#4285f4"
                      />
                    </view>
                    <!-- 权限信息 -->
                    <view class="permission-main-info">
                      <text class="permission-node-title">{{ node.permission_name }}</text>
                      <text class="permission-node-badge" :class="getPermissionTypeClass(node.permission_type)">
                        {{ getPermissionTypeText(node.permission_type) }}
                      </text>
                      <text 
                        class="permission-auth-status" 
                        :class="node.has_permission ? 'authorized' : 'unauthorized'"
                      >
                        {{ node.has_permission ? '已授权' : '未授权' }}
                      </text>
                    </view>
                  </view>
                </view>
                <!-- 子节点 -->
                <view
                  v-if="node.children && node.children.length > 0"
                  class="permission-children level-2"
                  :class="{ expanded: expandedViewIds.includes(node.id) }"
                >
                  <view v-for="child in node.children" :key="child.id" class="permission-node-container level-2">
                    <view class="permission-node">
                      <view class="permission-node-info">
                        <view
                          class="expand-icon"
                          v-if="child.children && child.children.length > 0"
                          @click="toggleExpandView(child.id)"
                        >
                          <wd-icon
                            :name="expandedViewIds.includes(child.id) ? 'arrow-down' : 'arrow-right'"
                            size="12"
                            color="#4285f4"
                          />
                        </view>
                        <view class="permission-main-info">
                          <text class="permission-node-title">{{ child.permission_name }}</text>
                          <text class="permission-node-badge" :class="getPermissionTypeClass(child.permission_type)">
                            {{ getPermissionTypeText(child.permission_type) }}
                          </text>
                          <text 
                            class="permission-auth-status" 
                            :class="child.has_permission ? 'authorized' : 'unauthorized'"
                          >
                            {{ child.has_permission ? '已授权' : '未授权' }}
                          </text>
                        </view>
                      </view>
                    </view>
                    <!-- 三级子节点 -->
                    <view
                      v-if="child.children && child.children.length > 0"
                      class="permission-children level-3"
                      :class="{ expanded: expandedViewIds.includes(child.id) }"
                    >
                      <view v-for="grandChild in child.children" :key="grandChild.id" class="permission-node-container level-3">
                        <view class="permission-node">
                          <view class="permission-node-info">
                            <view class="permission-main-info">
                              <text class="permission-node-title">{{ grandChild.permission_name }}</text>
                              <text class="permission-node-badge" :class="getPermissionTypeClass(grandChild.permission_type)">
                                {{ getPermissionTypeText(grandChild.permission_type) }}
                              </text>
                              <text 
                                class="permission-auth-status" 
                                :class="grandChild.has_permission ? 'authorized' : 'unauthorized'"
                              >
                                {{ grandChild.has_permission ? '已授权' : '未授权' }}
                              </text>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </wd-tab>

        <!-- 分配权限Tab -->
        <wd-tab title="分配权限" name="assign" class="tab-panel">
        <view v-if="loadingAssign" class="loading-state">
          <wd-loading />
          <text class="loading-text">加载可分配权限中...</text>
        </view>
        <view v-else-if="assignPermissionTree.length === 0" class="empty-state">
          <wd-status-tip type="content" tip="未给商户授权权限，暂无可分配的权限" />
        </view>
        <view v-else class="permission-container">
          <!-- 操作栏 -->
          <view class="permission-header">
            <view class="permission-actions">
              <wd-button size="small" @click="expandAllAssign">全部展开</wd-button>
              <wd-button size="small" @click="collapseAllAssign">全部收起</wd-button>
              <wd-button size="small" @click="checkAll">全选</wd-button>
              <wd-button size="small" @click="uncheckAll">取消全选</wd-button>
            </view>
          </view>
          <!-- 权限树 -->
          <view class="permission-tree">
            <view v-for="node in assignPermissionTree" :key="node.id" class="permission-node-container level-1">
              <view class="permission-node">
                <view class="permission-node-info">
                  <!-- 复选框 -->
                  <view class="checkbox-container" @click.stop>
                    <wd-checkbox
                      :model-value="assignCheckedIds.includes(node.id)"
                      :indeterminate="getIndeterminateState(node)"
                      @change="(checked) => handlePermissionCheck(node.id, checked, node)"
                    />
                  </view>
                  <!-- 展开/收起图标 -->
                  <view
                    class="expand-icon"
                    v-if="node.children && node.children.length > 0"
                    @click="toggleExpandAssign(node.id)"
                  >
                    <wd-icon
                      :name="expandedAssignIds.includes(node.id) ? 'arrow-down' : 'arrow-right'"
                      size="14"
                      color="#4285f4"
                    />
                  </view>
                  <!-- 权限信息 -->
                  <view class="permission-main-info">
                    <text class="permission-node-title">{{ node.permission_name }}</text>
                    <text class="permission-node-badge" :class="getPermissionTypeClass(node.permission_type)">
                      {{ getPermissionTypeText(node.permission_type) }}
                    </text>
                  </view>
                </view>
              </view>
              <!-- 子节点 -->
              <view
                v-if="node.children && node.children.length > 0"
                class="permission-children level-2"
                :class="{ expanded: expandedAssignIds.includes(node.id) }"
              >
                <view v-for="child in node.children" :key="child.id" class="permission-node-container level-2">
                  <view class="permission-node">
                    <view class="permission-node-info">
                      <view class="checkbox-container" @click.stop>
                        <wd-checkbox
                          :model-value="assignCheckedIds.includes(child.id)"
                          :indeterminate="getIndeterminateState(child)"
                          @change="(checked) => handlePermissionCheck(child.id, checked, child)"
                        />
                      </view>
                      <view
                        class="expand-icon"
                        v-if="child.children && child.children.length > 0"
                        @click="toggleExpandAssign(child.id)"
                      >
                        <wd-icon
                          :name="expandedAssignIds.includes(child.id) ? 'arrow-down' : 'arrow-right'"
                          size="12"
                          color="#4285f4"
                        />
                      </view>
                      <view class="permission-main-info">
                        <text class="permission-node-title">{{ child.permission_name }}</text>
                        <text class="permission-node-badge" :class="getPermissionTypeClass(child.permission_type)">
                          {{ getPermissionTypeText(child.permission_type) }}
                        </text>
                      </view>
                    </view>
                  </view>
                  <!-- 三级子节点 -->
                  <view
                    v-if="child.children && child.children.length > 0"
                    class="permission-children level-3"
                    :class="{ expanded: expandedAssignIds.includes(child.id) }"
                  >
                    <view v-for="grandChild in child.children" :key="grandChild.id" class="permission-node-container level-3">
                      <view class="permission-node">
                        <view class="permission-node-info">
                          <view class="checkbox-container" @click.stop>
                            <wd-checkbox
                              :model-value="assignCheckedIds.includes(grandChild.id)"
                              @change="(checked) => handlePermissionCheck(grandChild.id, checked, grandChild)"
                            />
                          </view>
                          <view class="permission-main-info">
                            <text class="permission-node-title">{{ grandChild.permission_name }}</text>
                            <text class="permission-node-badge" :class="getPermissionTypeClass(grandChild.permission_type)">
                              {{ getPermissionTypeText(grandChild.permission_type) }}
                            </text>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 底部保存按钮 -->
        <view class="bottom-actions" v-if="!loadingAssign && assignPermissionTree.length > 0">
          <wd-button type="primary" size="large" :loading="saving" @click="handleAssign">
            保存分配 ({{ assignCheckedIds.length }})
          </wd-button>
        </view>
        </wd-tab>
      </wd-tabs>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { 
  getMerchantRolePermissionsApi, 
  getAvailablePermissionsForRoleApi,
  assignPermissionsToRoleApi
} from '@/api/sys/systemMerchantRoleApi'
import type { 
  ISysMerchantRolePermissionsResponse,
  ISysMerchantRoleAvailablePermissionsResponse,
  IMerchantRolePermissionTreeNode,
  IMerchantRoleAvailablePermissionNode,
  ISysMerchantRolePermissionRequest
} from '@/api/sys/systemMerchantRoleApi'

const activeTab = ref('view')

// 页面参数
const roleId = ref<string>('')

// 查看权限Tab数据
const loadingView = ref(false)
const viewPermissionTree = ref<IMerchantRolePermissionTreeNode[]>([])
const expandedViewIds = ref<string[]>([])

// 分配权限Tab数据
const loadingAssign = ref(false)
const assignPermissionTree = ref<IMerchantRoleAvailablePermissionNode[]>([])
const assignCheckedIds = ref<string[]>([])
const expandedAssignIds = ref<string[]>([])
const saving = ref(false)

// 角色信息
const roleInfo = ref<{
  role_name: string
  merchant_name: string
  total_permissions?: number
  assigned_permissions?: number
}>({
  role_name: '',
  merchant_name: ''
})

// API调用方法
const loadViewPermissions = async () => {
  if (!roleId.value) return
  
  loadingView.value = true
  try {
    const response = await getMerchantRolePermissionsApi(roleId.value)
    if (response.data) {
      viewPermissionTree.value = response.data.permission_tree
      roleInfo.value = {
        role_name: response.data.role_name,
        merchant_name: response.data.merchant_name,
        total_permissions: response.data.total_permissions,
        assigned_permissions: response.data.assigned_permissions
      }
    }
  } catch (error) {
    console.error('加载权限数据失败:', error)
    uni.showToast({ title: '加载权限数据失败', icon: 'error' })
  } finally {
    loadingView.value = false
  }
}

const loadAssignPermissions = async () => {
  if (!roleId.value) return
  
  loadingAssign.value = true
  try {
    const response = await getAvailablePermissionsForRoleApi(roleId.value)
    if (response.data) {
      assignPermissionTree.value = response.data.permission_tree
      roleInfo.value = {
        role_name: response.data.role_name,
        merchant_name: response.data.merchant_name
      }
      
      // 初始化已分配的权限ID列表
      const assignedIds: string[] = []
      const collectAssignedIds = (nodes: IMerchantRoleAvailablePermissionNode[]) => {
        nodes.forEach(node => {
          if (node.is_assigned) {
            assignedIds.push(node.id)
          }
          if (node.children && node.children.length > 0) {
            collectAssignedIds(node.children)
          }
        })
      }
      collectAssignedIds(response.data.permission_tree)
      assignCheckedIds.value = assignedIds
    }
  } catch (error) {
    console.error('加载可分配权限失败:', error)
    uni.showToast({ title: '加载可分配权限失败', icon: 'error' })
  } finally {
    loadingAssign.value = false
  }
}

// 权限类型文本
const getPermissionTypeText = (type: number) => {
  switch (type) {
    case 1: return '目录'
    case 2: return '菜单'
    case 3: return '按钮'
    default: return '未知'
  }
}

// 权限类型样式
const getPermissionTypeClass = (type: number) => {
  switch (type) {
    case 1: return 'type-directory'
    case 2: return 'type-menu'
    case 3: return 'type-button'
    default: return ''
  }
}

// 获取节点的半选状态
const getIndeterminateState = (node: any) => {
  if (!node.children || node.children.length === 0) return false
  if (assignCheckedIds.value.includes(node.id)) return false

  const checkedChildren = node.children.filter((child: any) =>
    assignCheckedIds.value.includes(child.id)
  )
  return checkedChildren.length > 0 && checkedChildren.length < node.children.length
}

// 递归获取所有子节点ID
const getAllChildrenIds = (node: any): string[] => {
  const ids: string[] = []
  if (node.children && node.children.length > 0) {
    node.children.forEach((child: any) => {
      ids.push(child.id)
      ids.push(...getAllChildrenIds(child))
    })
  }
  return ids
}

// 处理权限选择
const handlePermissionCheck = (permissionId: string, checked: boolean | { value: boolean }, node: any) => {
  const isChecked = typeof checked === 'boolean' ? checked : checked.value
  let newCheckedIds = [...assignCheckedIds.value]

  if (isChecked) {
    // 选中当前节点和所有子节点
    if (!newCheckedIds.includes(permissionId)) {
      newCheckedIds.push(permissionId)
    }
    const childIds = getAllChildrenIds(node)
    childIds.forEach(id => {
      if (!newCheckedIds.includes(id)) {
        newCheckedIds.push(id)
      }
    })
  } else {
    // 取消当前节点和所有子节点
    newCheckedIds = newCheckedIds.filter(id => id !== permissionId)
    const childIds = getAllChildrenIds(node)
    newCheckedIds = newCheckedIds.filter(id => !childIds.includes(id))
  }

  assignCheckedIds.value = newCheckedIds
}

// 查看权限Tab操作
const toggleExpandView = (nodeId: string) => {
  const index = expandedViewIds.value.indexOf(nodeId)
  if (index > -1) {
    expandedViewIds.value.splice(index, 1)
  } else {
    expandedViewIds.value.push(nodeId)
  }
}

const expandAllView = () => {
  const allIds: string[] = []
  const collectIds = (nodes: any[]) => {
    nodes.forEach(node => {
      allIds.push(node.id)
      if (node.children && node.children.length > 0) {
        collectIds(node.children)
      }
    })
  }
  collectIds(viewPermissionTree.value)
  expandedViewIds.value = allIds
}

const collapseAllView = () => {
  expandedViewIds.value = []
}

const refreshViewData = async () => {
  await loadViewPermissions()
  uni.showToast({ title: '刷新成功', icon: 'success' })
}

// 分配权限Tab操作
const toggleExpandAssign = (nodeId: string) => {
  const index = expandedAssignIds.value.indexOf(nodeId)
  if (index > -1) {
    expandedAssignIds.value.splice(index, 1)
  } else {
    expandedAssignIds.value.push(nodeId)
  }
}

const expandAllAssign = () => {
  const allIds: string[] = []
  const collectIds = (nodes: any[]) => {
    nodes.forEach(node => {
      allIds.push(node.id)
      if (node.children && node.children.length > 0) {
        collectIds(node.children)
      }
    })
  }
  collectIds(assignPermissionTree.value)
  expandedAssignIds.value = allIds
}

const collapseAllAssign = () => {
  expandedAssignIds.value = []
}

const checkAll = () => {
  const allIds: string[] = []
  const collectIds = (nodes: any[]) => {
    nodes.forEach(node => {
      allIds.push(node.id)
      if (node.children && node.children.length > 0) {
        collectIds(node.children)
      }
    })
  }
  collectIds(assignPermissionTree.value)
  assignCheckedIds.value = allIds
}

const uncheckAll = () => {
  assignCheckedIds.value = []
}

// 保存分配
const handleAssign = async () => {
  if (!roleId.value) return
  saving.value = true
  try {
    // 构造权限分配请求参数
    const permissionRequest: ISysMerchantRolePermissionRequest = {
      authorized_permission_ids: assignCheckedIds.value
    }
    
    // 调用分配权限API
    await assignPermissionsToRoleApi(roleId.value, permissionRequest)
    uni.showToast({ title: '分配成功', icon: 'success' })
    
    // 重新加载查看权限数据以显示最新状态
    await loadViewPermissions()
  } catch (error) {
    console.error('分配权限失败:', error)
    uni.showToast({ title: '分配权限失败', icon: 'error' })
  } finally {
    saving.value = false
  }
}

// 返回
const handleBack = () => {
  uni.navigateBack()
}

// 获取页面参数
onShow(async () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}
  roleId.value = options.roleId || ''
  
  if (roleId.value) {
    // 加载权限数据
    await Promise.all([
      loadViewPermissions(),
      loadAssignPermissions()
    ])
  }
})
</script>

<style lang="scss" scoped>
.role-permission-manage-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 18px !important;
  font-weight: bold !important;
}

:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tab-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.permission-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.permission-header {
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  background-color: #fafafa;
}

.permission-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.permission-tree {
  flex: 1;
  padding: 8px 0 80px 0;
  overflow-y: auto;
}

.permission-node-container {
  margin-bottom: 2px;

  &.level-1 {
    background-color: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    margin: 4px 12px;
  }

  &.level-2 {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    margin: 2px 0;
  }

  &.level-3 {
    background-color: #fbfbfb;
    border-bottom: 1px solid #f0f0f0;
    margin: 1px 0;
  }
}

.permission-node {
  padding: 10px 12px;
}

.permission-node-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.expand-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  transition: background-color 0.2s ease;

  &:active {
    background-color: rgba(66, 133, 244, 0.1);
  }
}

.permission-main-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.permission-node-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.permission-node-badge {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  white-space: nowrap;

  &.type-directory {
    background-color: #fff3e0;
    color: #e65100;
  }

  &.type-menu {
    background-color: #e3f2fd;
    color: #1976d2;
  }

  &.type-button {
    background-color: #e8f5e8;
    color: #388e3c;
  }
}

.permission-auth-status {
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;

  &.authorized {
    background-color: #e8f5e8;
    color: #4caf50;
    border: 1px solid #c8e6c9;
  }

  &.unauthorized {
    background-color: #fff3e0;
    color: #ff9800;
    border: 1px solid #ffcc80;
  }
}

.permission-children {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease;
  padding-left: 20px;
  border-left: 2px solid #e0e0e0;
  margin-left: 16px;

  &.expanded {
    max-height: 2000px;
    opacity: 1;
  }

  &.level-3 {
    margin-top: 4px;
    padding-left: 16px;
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 20px;
  gap: 16px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  min-height: 200px;
}

.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  border-top: 1px solid #eee;
  padding: 12px 20px calc(12px + env(safe-area-inset-bottom));
  z-index: 10;
}
</style> 