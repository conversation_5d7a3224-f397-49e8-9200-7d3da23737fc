/**
 * 系统商户用户管理相关常量
 */

/**
 * 分页查询默认参数
 */
export const SysMerchantUserPageConstants = {
  // 默认页码
  DEFAULT_PAGE: 1,
  // 默认每页大小
  DEFAULT_PAGE_SIZE: 10,
  // 最大每页大小
  MAX_PAGE_SIZE: 100,
  // 最小每页大小
  MIN_PAGE_SIZE: 1,
} as const

/**
 * 商户用户状态枚举及映射
 */
export enum SysMerchantUserStatus {
  // 启用
  ENABLED = 1,
  // 禁用
  DISABLED = 2,
  // 锁定
  LOCKED = 3,
}

export const SysMerchantUserStatusMap = {
  [SysMerchantUserStatus.ENABLED]: '启用',
  [SysMerchantUserStatus.DISABLED]: '禁用',
  [SysMerchantUserStatus.LOCKED]: '锁定',
} as const

/**
 * 商户用户性别枚举及映射
 */
export enum SysMerchantUserGender {
  // 男
  MALE = 1,
  // 女
  FEMALE = 2,
  // 未知
  UNKNOWN = 3,
}

export const SysMerchantUserGenderMap = {
  [SysMerchantUserGender.MALE]: '男',
  [SysMerchantUserGender.FEMALE]: '女',
  [SysMerchantUserGender.UNKNOWN]: '未知',
} as const

/**
 * 商户用户角色类型枚举及映射
 */
export enum SysMerchantUserRoleType {
  // 管理员角色
  ADMIN = 1,
  // 自定义角色
  CUSTOM = 2,
}

export const SysMerchantUserRoleTypeMap = {
  [SysMerchantUserRoleType.ADMIN]: '管理员角色',
  [SysMerchantUserRoleType.CUSTOM]: '自定义角色',
} as const

/**
 * 商户用户角色状态枚举及映射
 */
export enum SysMerchantUserRoleStatus {
  // 禁用
  DISABLED = 0,
  // 启用
  ENABLED = 1,
}

export const SysMerchantUserRoleStatusMap = {
  [SysMerchantUserRoleStatus.DISABLED]: '禁用',
  [SysMerchantUserRoleStatus.ENABLED]: '启用',
} as const

/**
 * 商户用户验证规则常量
 */
export const SysMerchantUserValidation = {
  // 用户名
  USERNAME_MIN_LENGTH: 3,
  USERNAME_MAX_LENGTH: 20,
  USERNAME_PATTERN: /^[a-zA-Z0-9_]{3,20}$/,

  // 密码
  PASSWORD_MIN_LENGTH: 6,
  PASSWORD_MAX_LENGTH: 20,
  PASSWORD_PATTERN: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,20}$/,

  // 手机号
  PHONE_PATTERN: /^1[3-9]\d{9}$/,

  // 邮箱
  EMAIL_PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,

  // 真实姓名
  REAL_NAME_MAX_LENGTH: 10,
  REAL_NAME_PATTERN: /^[\u4e00-\u9fa5a-zA-Z\s]{1,10}$/,

  // 身份证号
  ID_CARD_PATTERN: /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,

  // 佣金比例
  COMMISSION_RATE_MIN: 0,
  COMMISSION_RATE_MAX: 1,
  COMMISSION_RATE_PATTERN: /^0(\.\d{1,4})?$|^1(\.0{1,4})?$/,
} as const