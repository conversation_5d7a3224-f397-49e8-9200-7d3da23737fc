<template>
  <view>
    <!-- 固定定位时的占位容器 -->
    <view v-if="fixed && placeholder" class="navbar-placeholder" :style="placeholderStyle"></view>

    <!-- 导航栏主体 -->
    <view class="navbar" :style="navbarStyle">
      <!-- 状态栏占位 -->
      <view class="status-bar" :style="{ height: safeAreaInsetsTop + 'px' }"></view>

      <!-- 导航栏内容区 -->
      <view class="navbar-content" :style="contentStyle">
        <!-- 左侧区域 -->
        <view class="navbar-left">
          <slot name="left">
            <!-- 默认返回按钮 -->
            <view v-if="showBack" class="navbar-back" @click="handleBack">
              <wd-icon
                name="arrow-left"
                :color="iconColor"
                custom-class="navbar-back-icon"
                :custom-style="`font-size: 24px !important;`"
              />
            </view>
          </slot>
        </view>

        <!-- 中间区域 -->
        <view class="navbar-center">
          <slot name="center">
            <!-- 默认标题 -->
            <text v-if="title" class="navbar-title" :style="titleStyle">{{ title }}</text>
          </slot>
        </view>

        <!-- 右侧区域 -->
        <view class="navbar-right" :style="rightStyle">
          <slot name="right">
            <!-- 右侧默认为空 -->
          </slot>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, CSSProperties } from 'vue'
import { useGlobalSafeArea } from '../../hooks/useSafeArea'

interface Props {
  // 是否显示返回按钮
  showBack?: boolean
  // 标题
  title?: string
  // 背景色
  backgroundColor?: string
  // 文字颜色
  color?: string
  // 图标颜色
  iconColor?: string
  // 是否固定在顶部
  fixed?: boolean
  // 是否显示底部边框
  border?: boolean
  // 自定义高度
  height?: number
  // z-index
  zIndex?: number
  // 是否占位（fixed时有效）
  placeholder?: boolean
  // 自定义类名
  customClass?: string
  // 自定义样式
  customStyle?: string
}

const props = withDefaults(defineProps<Props>(), {
  showBack: true,
  title: '',
  backgroundColor: '#ffffff',
  color: '#333333',
  iconColor: '#333333',
  fixed: false,
  border: true,
  height: 44,
  zIndex: 999,
  placeholder: true,
  customClass: '',
  customStyle: '',
})

const emit = defineEmits(['back'])

// 使用全局安全区域 hook
const { safeAreaInsetsTop, rightSafeArea } = useGlobalSafeArea()

// 系统信息
const menuButtonInfo = ref<any>(null)
const navbarHeight = ref(44)

// 总高度（状态栏 + 导航栏）
const totalHeight = computed(() => {
  return safeAreaInsetsTop.value + navbarHeight.value
})

// 占位容器样式
const placeholderStyle = computed<CSSProperties>(() => {
  return {
    height: totalHeight.value + 'px',
  }
})

// 导航栏样式
const navbarStyle = computed<CSSProperties>(() => {
  const style: CSSProperties = {
    backgroundColor: props.backgroundColor,
    zIndex: props.zIndex,
  }

  if (props.fixed) {
    style.position = 'fixed'
    style.top = '0'
    style.left = '0'
    style.right = '0'
  }

  if (props.border) {
    style.borderBottom = '1px solid #e5e5e5'
  }

  return style
})

// 内容区样式
const contentStyle = computed<CSSProperties>(() => {
  const style: CSSProperties = {
    height: navbarHeight.value + 'px',
    position: 'relative',
  }

  return style
})

// 右侧区域样式
const rightStyle = computed<CSSProperties>(() => {
  const style: CSSProperties = {}

  // 微信小程序需要避开胶囊按钮
  // #ifdef MP-WEIXIN
  if (menuButtonInfo.value) {
    const systemInfo = uni.getSystemInfoSync()
    const rightDistance = systemInfo.windowWidth - menuButtonInfo.value.left + 10 // 加10px间距
    style.width = rightDistance + 'px'
  }
  // #endif

  return style
})

// 标题样式
const titleStyle = computed<CSSProperties>(() => {
  return {
    color: props.color,
  }
})

// 返回操作
const handleBack = () => {
  emit('back')
  // 默认返回行为
  const pages = getCurrentPages()
  if (pages.length > 1) {
    uni.navigateBack()
  } else {
    // 如果是第一个页面，返回到首页
    uni.reLaunch({
      url: '/pages/user/index',
    })
  }
}

// 初始化
onMounted(() => {
  // #ifdef MP-WEIXIN
  // 获取胶囊按钮信息来计算导航栏高度
  const menuButton = uni.getMenuButtonBoundingClientRect()
  if (menuButton) {
    menuButtonInfo.value = menuButton
    // 计算导航栏高度（胶囊按钮底部位置 - 状态栏高度 + 胶囊按钮顶部位置与状态栏的间距）
    const gap = menuButton.top - safeAreaInsetsTop.value
    navbarHeight.value = menuButton.height + gap * 2
  }
  // #endif

  // #ifndef MP-WEIXIN
  // 非微信小程序使用默认高度或自定义高度
  navbarHeight.value = props.height
  // #endif
})
</script>

<style lang="scss" scoped>
.navbar {
  width: 100%;
  background-color: #fff;
}

.status-bar {
  width: 100%;
}

.navbar-content {
  display: grid;
  grid-template-columns: 1fr auto 1fr; // 三栏等比布局
  align-items: center;
  height: 100%;
  padding: 0 12px;
  box-sizing: border-box;
}

.navbar-left {
  display: flex;
  align-items: center;
  justify-self: flex-start;
}

.navbar-center {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  padding: 0 10px;
}

.navbar-right {
  display: flex;
  align-items: center;
  justify-self: flex-end;
}

.navbar-back {
  width: 44px; // 调整为合适大小
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: -8px; // 让点击区域更贴近边缘

  &:active {
    opacity: 0.7;
  }
}

// 为微信小程序添加全局样式（不使用 deep）
.navbar-back-icon {
  font-size: 24px !important;

  // 微信小程序中可能需要这些
  // #ifdef MP-WEIXIN
  &::before {
    font-size: 24px !important;
  }
  // #endif
}

.navbar-title {
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #333;
}

/* 占位容器 */
.navbar-placeholder {
  width: 100%;
}
</style>
