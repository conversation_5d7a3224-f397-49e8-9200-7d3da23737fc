/**
 * 角色分页查询请求参数
 */
export interface ISysRolePageRequest {
  // 当前页码 (1开始)
  page?: number | null
  // 每页大小
  page_size?: number | null
  // 角色名称关键字搜索
  name?: string | null
  // 角色描述关键字搜索
  description?: string | null
  // 创建时间开始 (YYYY-MM-DD格式)
  created_start?: string | null
  // 创建时间结束 (YYYY-MM-DD格式)
  created_end?: string | null
}

/**
 * 角色列表响应VO (用于分页查询)
 */
export interface ISysRoleListResponse {
  // 角色ID
  id: string
  // 角色名称
  name: string
  // 角色描述
  description?: string | null
  // 拥有该角色的用户数量
  user_count: number
  // 角色拥有的权限数量
  permission_count: number
  // 创建时间
  created_date: string
  // 更新时间
  updated_date: string
  // 备注
  remark?: string | null
}

/**
 * 角色简单信息VO（用于下拉选择等场景）
 */
export interface ISysRoleSimpleResponse {
  // 角色ID
  id: string
  // 角色名称
  name: string
  // 该角色下用户数量
  user_count: number
  // 角色描述
  description?: string | null
}

/**
 * 用户角色信息（用于用户信息中的角色数据）
 */
export interface IUserRoleInfo {
  // 角色ID
  id: string
  // 角色名称
  name: string
  // 角色代码
  role_code: string
  // 角色描述
  description: string
}

/**
 * 角色权限详情VO
 */
export interface ISysRolePermissionVo {
  // 权限ID
  id: string
  // 权限名称
  menu_name: string
  // 权限标识
  perms?: string | null
  // 权限类型 1:目录，2:菜单，3:按钮
  menu_type?: number | null
  // 父级权限ID
  parent_id?: string | null
}

/**
 * 角色用户详情VO
 */
export interface ISysRoleUserVo {
  // 用户ID
  id: string
  // 用户名
  username: string
  // 真实姓名
  real_name?: string | null
  // 邮箱
  email?: string | null
  // 用户状态 1:启用 2:禁用 3:锁定
  status: number
}

/**
 * 角色详情响应VO (包含完整信息)
 */
export interface ISysRoleDetailResponse {
  // 角色ID
  id: string
  // 角色名称
  name: string
  // 角色描述
  description?: string | null
  // 创建时间
  created_date: string
  // 更新时间
  updated_date: string
  // 创建人ID
  created_by?: string | null
  // 更新人ID
  updated_by?: string | null
  // 备注
  remark?: string | null
  // 角色拥有的权限列表
  permissions: ISysRolePermissionVo[]
  // 拥有该角色的用户列表
  users: ISysRoleUserVo[]
}

/**
 * 角色创建请求DTO
 */
export interface ISysRoleCreateRequest {
  // 角色名称
  name: string
  // 角色描述
  description?: string | null
  // 权限ID集合
  permission_ids?: string[] | null
  // 备注
  remark?: string | null
}

/**
 * 角色更新请求DTO
 */
export interface ISysRoleUpdateRequest {
  // 角色名称
  name: string
  // 角色描述
  description?: string | null
  // 备注
  remark?: string | null
}

/**
 * 角色权限分配请求DTO
 */
export interface ISysRolePermissionRequest {
  // 权限ID集合
  permission_ids: string[]
}

/**
 * 批量删除角色请求DTO
 */
export interface ISysRoleBatchDeleteRequest {
  // 角色ID集合
  role_ids: string[]
}
