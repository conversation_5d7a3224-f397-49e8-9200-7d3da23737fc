import { http } from '@/utils/http'
import type {
  ISysMerchantCategoryPageRequest,
  ISysMerchantCategoryListResponse,
  ISysMerchantCategoryDetailResponse,
  ISysMerchantCategoryCreateRequest,
  ISysMerchantCategoryUpdateRequest,
  ISysMerchantCategoryBatchDeleteRequest,
  ISysMerchantCategoryStatusRequest,
  ISysMerchantCategorySelectItem,
  ISysMerchantCategoryStatsResponse,
} from './types'

/**
 * 分页查询商户分类列表
 * @param params 查询参数
 * @returns 返回分页商户分类列表
 */
export const getSystemMerchantCategoriesApi = (params?: ISysMerchantCategoryPageRequest) => {
  return http.get<IPageData<ISysMerchantCategoryListResponse>>(
    '/business/merchant-categories',
    params,
  )
}

/**
 * 创建商户分类
 * @param createRequest 创建请求参数
 * @returns 返回创建结果
 */
export const createSystemMerchantCategoryApi = (
  createRequest: ISysMerchantCategoryCreateRequest,
) => {
  return http.post<string>('/business/merchant-categories', createRequest)
}

/**
 * 批量删除商户分类
 * @param batchDeleteRequest 批量删除请求
 * @returns 返回批量删除结果
 */
export const batchDeleteSystemMerchantCategoriesApi = (
  batchDeleteRequest: ISysMerchantCategoryBatchDeleteRequest,
) => {
  return http.post<string>('/business/merchant-categories/batch-delete', batchDeleteRequest)
}

/**
 * 获取商户分类选择项
 * @returns 返回用于下拉选择的商户分类列表
 */
export const getSystemMerchantCategorySelectItemsApi = () => {
  return http.get<ISysMerchantCategorySelectItem[]>('/business/merchant-categories/select')
}

/**
 * 获取商户分类统计信息
 * @returns 返回商户分类统计数据
 */
export const getSystemMerchantCategoryStatsApi = () => {
  return http.get<ISysMerchantCategoryStatsResponse[]>('/business/merchant-categories/stats')
}

/**
 * 查询商户分类详情
 * @param categoryId 分类ID
 * @returns 返回商户分类详情信息
 */
export const getSystemMerchantCategoryDetailApi = (categoryId: string) => {
  return http.get<ISysMerchantCategoryDetailResponse>(
    `/business/merchant-categories/${categoryId}`,
  )
}

/**
 * 更新商户分类信息
 * @param categoryId 分类ID
 * @param updateRequest 更新请求参数
 * @returns 返回更新结果
 */
export const updateSystemMerchantCategoryApi = (
  categoryId: string,
  updateRequest: ISysMerchantCategoryUpdateRequest,
) => {
  return http.put<string>(`/business/merchant-categories/${categoryId}`, updateRequest)
}

/**
 * 删除商户分类
 * @param categoryId 分类ID
 * @returns 返回删除结果
 */
export const deleteSystemMerchantCategoryApi = (categoryId: string) => {
  return http.delete<string>(`/business/merchant-categories/${categoryId}`)
}

/**
 * 切换商户分类状态
 * @param categoryId 分类ID
 * @param statusRequest 状态切换请求
 * @returns 返回切换结果
 */
export const changeSystemMerchantCategoryStatusApi = (
  categoryId: string,
  statusRequest: ISysMerchantCategoryStatusRequest,
) => {
  return http.put<string>(`/business/merchant-categories/${categoryId}/status`, statusRequest)
}

// 同时导出接口类型，方便外部使用
export type {
  ISysMerchantCategoryPageRequest,
  ISysMerchantCategoryListResponse,
  ISysMerchantCategoryDetailResponse,
  ISysMerchantCategoryCreateRequest,
  ISysMerchantCategoryUpdateRequest,
  ISysMerchantCategoryBatchDeleteRequest,
  ISysMerchantCategoryStatusRequest,
  ISysMerchantCategorySelectItem,
  ISysMerchantCategoryStatsResponse,
} 