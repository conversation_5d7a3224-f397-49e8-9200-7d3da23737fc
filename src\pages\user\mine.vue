<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '我的',
  },
  access: {
    requireAuth: false,
  },
}
</route>

<template>
  <scroll-view
    class="mine-container"
    scroll-y
    @scrolltolower="onReachBottom"
    :lower-threshold="200"
  >
    <!-- 顶部渐变背景 -->
    <view class="header-background" :style="{ paddingTop: safeAreaInsetsTop + 'px' }">
      <!-- 平台公告 -->
      <view class="announcement-section" :style="announcementSectionStyle">
        <wd-notice-bar
          :text="announcementTexts"
          prefix="notification"
          :scrollable="true"
          :delay="2"
          :speed="60"
          color="#ffffff"
          background-color="rgba(255, 255, 255, 0.2)"
          custom-class="announcement-notice"
        />
      </view>

      <!-- 用户信息卡片 -->
      <view class="user-card">
        <!-- 右箭头 - 右上角 -->
        <view class="arrow-icon">
          <wd-icon name="arrow-right" size="14px" color="#999" />
        </view>

        <!-- 用户头像 - 凸出卡片 -->
        <view class="avatar-container">
          <wd-img
            class="user-avatar"
            src="/static/images/default-avatar.png"
            mode="aspectFill"
            round
            :width="52"
            :height="52"
          />
        </view>

        <!-- 用户信息 -->
        <view class="user-info">
          <view class="user-name-section">
            <text class="user-name">用户昵称</text>
          </view>
        </view>

        <!-- 用户收藏统计 -->
        <view class="collection-stats">
          <view class="stat-item" @click="goToFavoriteShops">
            <wd-badge modelValue="12" type="primary">
              <wd-icon name="shop" color="#1890ff" size="18px" />
            </wd-badge>
            <text class="stat-label">收藏店铺</text>
          </view>
          <view class="stat-item" @click="goToFavoriteGoods">
            <wd-badge modelValue="28" type="success">
              <wd-icon name="goods" color="#52c41a" />
            </wd-badge>
            <text class="stat-label">收藏商品</text>
          </view>
          <view class="stat-item" @click="goToHistory">
            <wd-badge modelValue="156" type="warning">
              <wd-icon name="time" color="#faad14" size="18px" />
            </wd-badge>
            <text class="stat-label">历史浏览</text>
          </view>
        </view>
      </view>

      <!-- 功能网格 -->
      <view class="function-grid-container">
        <view class="function-grid-item orders-item">
          <view class="function-icon-wrapper">
            <wd-icon name="list" />
          </view>
          <text class="function-text">我的订单</text>
        </view>
        <view class="function-grid-item feedback-item" @click="goToFeedback">
          <view class="function-icon-wrapper">
            <view
              class="iconfont-sys iconsys-fankui_2"
              :style="{
                fontSize: '20px',
                color: '#1890ff',
              }"
            ></view>
          </view>
          <text class="function-text">反馈</text>
        </view>
        <view class="function-grid-item help-center-item" @click="goToHelpCenter">
          <view class="function-icon-wrapper">
            <wd-icon name="service" />
          </view>
          <text class="function-text">帮助中心</text>
        </view>
        <view class="function-grid-item customer-service-item" @click="goToCustomerService">
          <view class="function-icon-wrapper">
            <wd-icon name="chat" />
          </view>
          <text class="function-text">智能客服</text>
        </view>
        <view class="function-grid-item settings-item" @click="goToSettings">
          <view class="function-icon-wrapper">
            <wd-icon name="setting" />
          </view>
          <text class="function-text">设置</text>
        </view>
      </view>
    </view>

    <!-- 猜你喜欢 -->
    <view class="recommend-section">
      <view class="section-header">
        <text class="section-title">猜你喜欢</text>
        <text class="section-more" @click="goToRecommendMore">更多</text>
      </view>

      <!-- 使用瀑布流商品组件 -->
      <WaterfallGoods
        :goods-list="goodsList"
        :loading="loading"
        :has-more="hasMore"
        :column-count="2"
        :show-price="false"
        loading-text="加载中..."
        no-more-text="没有更多商品了"
        @goods-click="handleGoodsClick"
        @like-click="handleLikeClick"
      />
    </view>

    <!-- 功能卡片区域 -->
    <view class="function-cards">
      <!-- 角色切换区域（开发测试用） -->
      <view class="role-switch-section">
        <text class="section-title">开发测试 - 角色切换</text>
        <view class="button-group">
          <wd-button
            :type="globalRoleStore.getRole.valueOf() === 'user' ? 'primary' : 'default'"
            size="small"
            @click="switchRole(userTypeEnum.user)"
          >
            普通用户
          </wd-button>
          <wd-button
            :type="globalRoleStore.getRole.valueOf() === 'merchants' ? 'primary' : 'default'"
            size="small"
            @click="switchRole(userTypeEnum.merchants)"
          >
            商户用户
          </wd-button>
          <wd-button
            :type="globalRoleStore.getRole.valueOf() === 'system' ? 'primary' : 'default'"
            size="small"
            @click="switchRole(userTypeEnum.system)"
          >
            管理员
          </wd-button>
        </view>
        <text class="current-role">当前角色：{{ currentRole }}</text>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <TabBar current-page="mine" />
  </scroll-view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { userTypeEnum, useGlobalRole } from '@/store/globalRole'
import { useGlobalSafeArea } from '@/hooks/useSafeArea'
import { isMp } from '@/utils/platform'
import WaterfallGoods from '@/components/waterfall-goods/index.vue'
import type { GoodsItem } from '@/components/waterfall-goods/types'

const globalRoleStore = useGlobalRole()
const { safeAreaInsetsTop, rightSafeArea } = useGlobalSafeArea()

// 公告区域样式
const announcementSectionStyle = computed(() => {
  const style: any = {
    paddingRight: '20px',
  }

  // 小程序平台需要避开胶囊
  if (isMp && rightSafeArea.value > 0) {
    style.paddingRight = rightSafeArea.value + 'px'
  }

  return style
})

defineOptions({
  name: 'UserMine',
})

const currentRole = computed(() => {
  return getRoleText(globalRoleStore.getRole)
})

// 公告文本数据
const announcementTexts = ref([
  '测试测试测试测试测试测试测试测试测试',
  '测试测试测试测试测试测试测试测试测试',
  '测试测试测试测试测试测试测试测试测试',
  '客服热线：400-123-4567，为您提供贴心服务！',
])

// 商品数据
const goodsList = ref<GoodsItem[]>([
  {
    id: 1,
    title: '时尚简约连衣裙 春夏新款 舒适透气',
    price: 299,
    originalPrice: 399,
    color: '#FF6B6B',
    sales: 1234,
    isLiked: false,
    imageHeight: 180,
  },
  {
    id: 2,
    title: '高品质真皮手提包 商务休闲两用',
    price: 599,
    originalPrice: 799,
    color: '#4ECDC4',
    sales: 567,
    isLiked: true,
    imageHeight: 220,
  },
  {
    id: 3,
    title: '运动休闲鞋 透气舒适 多色可选',
    price: 199,
    originalPrice: null,
    color: '#45B7D1',
    sales: 890,
    isLiked: false,
    imageHeight: 160,
  },
  {
    id: 4,
    title: '智能手表 健康监测 长续航',
    price: 899,
    originalPrice: 1299,
    color: '#96CEB4',
    sales: 345,
    isLiked: false,
    imageHeight: 200,
  },
  {
    id: 5,
    title: '无线蓝牙耳机 降噪音质好',
    price: 399,
    originalPrice: 599,
    color: '#FFEAA7',
    sales: 678,
    isLiked: true,
    imageHeight: 170,
  },
  {
    id: 6,
    title: '护肤套装 补水保湿 温和不刺激',
    price: 259,
    originalPrice: 359,
    color: '#DDA0DD',
    sales: 1567,
    isLiked: false,
    imageHeight: 190,
  },
])

// 无限滚动相关状态
const loading = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)
const pageSize = 10

// 生成随机商品数据
const generateRandomGoods = (startId: number, count: number): GoodsItem[] => {
  const titles = [
    '时尚简约连衣裙 春夏新款 舒适透气',
    '高品质真皮手提包 商务休闲两用',
    '运动休闲鞋 透气舒适 多色可选',
    '智能手表 健康监测 长续航',
    '无线蓝牙耳机 降噪音质好',
    '护肤套装 补水保湿 温和不刺激',
    '时尚太阳镜 防紫外线 潮流款式',
    '舒适家居服 纯棉材质 亲肤柔软',
    '多功能背包 大容量 商务休闲',
    '精美首饰盒 收纳整理 送礼佳品',
    '健康养生茶 天然草本 清香怡人',
    '智能音响 高音质 语音控制',
    '创意台灯 护眼设计 简约美观',
    '优质床品 四件套 舒适睡眠',
    '厨房用品 不锈钢材质 实用耐用',
  ]

  const colors = [
    '#FF6B6B',
    '#4ECDC4',
    '#45B7D1',
    '#96CEB4',
    '#FFEAA7',
    '#DDA0DD',
    '#98D8C8',
    '#F7DC6F',
    '#BB8FCE',
    '#85C1E9',
    '#F8C471',
    '#82E0AA',
    '#F1948A',
    '#85C1E9',
    '#D7BDE2',
  ]

  const goods: GoodsItem[] = []
  for (let i = 0; i < count; i++) {
    const id = startId + i
    const randomTitleIndex = Math.floor(Math.random() * titles.length)
    const randomPrice = Math.floor(Math.random() * 800) + 50
    const randomOriginalPrice =
      Math.random() > 0.3 ? randomPrice + Math.floor(Math.random() * 200) + 50 : null
    const randomSales = Math.floor(Math.random() * 2000) + 10
    const randomHeight = Math.floor(Math.random() * 100) + 150
    const randomColorIndex = Math.floor(Math.random() * colors.length)

    goods.push({
      id,
      title: titles[randomTitleIndex],
      price: randomPrice,
      originalPrice: randomOriginalPrice,
      color: colors[randomColorIndex],
      sales: randomSales,
      isLiked: Math.random() > 0.7,
      imageHeight: randomHeight,
    })
  }
  return goods
}

// 加载更多商品
const loadMoreGoods = async () => {
  if (loading.value || !hasMore.value) return

  loading.value = true

  // 模拟网络请求延迟（减少延迟以提升用户体验）
  await new Promise((resolve) => setTimeout(resolve, 10))

  const newGoods = generateRandomGoods(goodsList.value.length + 1, pageSize)
  goodsList.value.push(...newGoods)

  currentPage.value++

  // 模拟数据有限，加载到100个商品后停止
  if (goodsList.value.length >= 100) {
    hasMore.value = false
  }

  loading.value = false
}

// 页面触底事件
const onReachBottom = () => {
  loadMoreGoods()
}

// 页面加载时初始化
onMounted(() => {
  // 初始加载更多数据
  loadMoreGoods()
})

// 瀑布流列数
const columnCount = 2

// 商品点击处理（替换原来的goToGoodsDetail）
const handleGoodsClick = (item: GoodsItem) => {
  uni.navigateTo({
    url: `/pages/user/goods-detail?id=${item.id}&title=${encodeURIComponent(item.title)}`,
  })
}

// 点赞处理
const handleLikeClick = (item: GoodsItem) => {
  // 找到对应的商品并切换点赞状态
  const index = goodsList.value.findIndex((goods) => goods.id === item.id)
  if (index !== -1) {
    goodsList.value[index].isLiked = !goodsList.value[index].isLiked
    uni.showToast({
      title: goodsList.value[index].isLiked ? '已收藏' : '已取消收藏',
      icon: 'success',
      duration: 1000,
    })
  }
}

// 切换角色
const switchRole = (role: userTypeEnum) => {
  globalRoleStore.setRole(role)
  uni.showToast({
    title: `已切换到${getRoleText(role)}`,
    icon: 'success',
  })
}

// 获取角色显示文本
const getRoleText = (role: userTypeEnum) => {
  const roleMap = {
    user: '普通用户',
    merchants: '商户用户',
    system: '管理员',
  }
  return roleMap[role]
}

// 跳转到订单页面
const goToOrders = (type: string) => {
  uni.navigateTo({
    url: `/pages/user/orders?type=${type}`,
  })
}

// 跳转到收藏店铺
const goToFavoriteShops = () => {
  uni.navigateTo({
    url: '/pages/user/favorite-shops',
  })
}

// 跳转到收藏商品
const goToFavoriteGoods = () => {
  uni.navigateTo({
    url: '/pages/user/favorite-goods',
  })
}

// 跳转到历史浏览
const goToHistory = () => {
  uni.navigateTo({
    url: '/pages/user/history',
  })
}

// 跳转到帮助反馈
const goToFeedback = () => {
  uni.navigateTo({
    url: '/pages/user/feedback',
  })
}

// 跳转到在线客服
const goToCustomerService = () => {
  uni.navigateTo({
    url: '/pages/user/customer-service',
  })
}

// 跳转到系统设置
const goToSettings = () => {
  uni.navigateTo({
    url: '/pages/user/settings',
  })
}

// 跳转到推荐更多
const goToRecommendMore = () => {
  uni.navigateTo({
    url: '/pages/goods/recommend',
  })
}

// 跳转到帮助中心
const goToHelpCenter = () => {
  uni.navigateTo({
    url: '/pages/user/help-center',
  })
}

// 跳转到我的钱包
const goToWallet = () => {
  uni.navigateTo({
    url: '/pages/user/wallet',
  })
}

// 跳转到优惠券
const goToCoupon = () => {
  uni.navigateTo({
    url: '/pages/user/coupon',
  })
}

// 跳转到收货地址
const goToAddress = () => {
  uni.navigateTo({
    url: '/pages/user/address',
  })
}

// 跳转到商户主页（测试用）
const goToMerchantHome = () => {
  uni.navigateTo({
    url: '/pages/user/merchant-home?id=1',
  })
}
</script>

<style lang="scss" scoped>
.mine-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// 顶部渐变背景
.header-background {
  background: linear-gradient(180deg, #1890ff 0%, #40a9ff 40%, transparent 100%);
  //background: linear-gradient(180deg, #ff6918 0%, #c02626 40%, transparent 100%);
  position: relative;
}

// 平台公告
.announcement-section {
  padding: 12px 20px;
}

:deep(.announcement-notice) {
  height: 36px !important;
  overflow: hidden !important;
}

:deep(.announcement-notice .wd-notice-bar__wrap) {
  border-radius: 20px !important;
  height: 36px !important;
}

:deep(.announcement-notice .wd-notice-bar__content) {
  height: 36px !important;
  line-height: 36px !important;
  font-size: 13px !important;
  color: #ffffff !important;
}

:deep(.announcement-notice .wd-notice-bar__text) {
  color: #ffffff !important;
  font-size: 13px !important;
}

:deep(.announcement-notice .wd-icon) {
  color: #ffffff !important;
  font-size: 16px !important;
}

// 用户信息卡片
.user-card {
  margin: 10px 20px 0 20px;
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  position: relative;
}

// 右箭头 - 右上角
.arrow-icon {
  position: absolute;
  top: 15px;
  right: 25px;
  transition: transform 0.2s;

  &:active {
    transform: scale(0.9);
  }
}

// 用户头像容器 - 凸出效果
.avatar-container {
  position: absolute;
  top: -13px;
  left: 15px;
  width: 55px;
  height: 55px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.user-avatar {
  width: 50px !important;
  height: 50px !important;
  border-radius: 50% !important;
  overflow: hidden !important;
}

// 用户信息
.user-info {
  margin-left: 70px;
  margin-bottom: 16px;
  margin-top: -10px;
}

.user-name-section {
  margin-bottom: 4px;
}

.user-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

// 收藏统计
.collection-stats {
  display: flex;
  justify-content: space-around;
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
  // 图标颜色和大小设置
  :deep(.wd-icon-shop) {
    color: #1890ff !important;
    font-size: 18px !important;
  }

  :deep(.wd-icon-goods) {
    color: #52c41a !important;
  }

  :deep(.wd-icon-time) {
    color: #faad14 !important;
    font-size: 18px !important;
  }
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  transition: transform 0.2s;

  &:active {
    transform: scale(0.95);
  }
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 0;
}

// 徽标样式调整
:deep(.collection-stats .wd-badge) {
  .wd-badge__content {
    min-width: 14px !important;
    height: 14px !important;
    font-size: 10px !important;
    line-height: 14px !important;
    padding: 0 4px !important;
  }
}

/* 功能列表样式 */
.function-list {
  background: white;
  border-radius: 12px;
  margin: 16px;
  padding: 0;
}

.function-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.2s;
}

.function-list-item:last-child {
  border-bottom: none;
}

.function-list-item:active {
  background-color: #f8f9fa;
}

.item-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.item-text {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

// 功能卡片区域
.function-cards {
  flex: 1;
  padding: 0 20px 20px 20px;
  overflow-y: auto;
}

// 功能卡片
.function-card {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 26px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.card-more {
  font-size: 14px;
  color: #1890ff;
}

// 功能网格容器
.function-grid-container {
  display: flex;
  flex-wrap: wrap;
  background: #fff;
  border-radius: 10px;
  margin: 15px 20px 20px 20px;
}

.function-grid-item {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 4px;
  position: relative;
  transition: transform 0.2s;
  box-sizing: border-box;

  &:active {
    transform: scale(0.95);
  }
}

// 使用:deep()设置各个功能项图标颜色和大小
.feedback-item {
  :deep(.wd-icon-iconsys-fankui_2) {
    color: #1890ff !important;
  }
}

.help-center-item {
  :deep(.wd-icon-service) {
    color: #52c41a !important;
    font-size: 20px !important;
  }
}

.customer-service-item {
  :deep(.wd-icon-chat) {
    color: #faad14 !important;
    font-size: 20px !important;
  }
}

.settings-item {
  :deep(.wd-icon-setting) {
    color: #722ed1 !important;
    font-size: 20px !important;
  }
}

.orders-item {
  :deep(.wd-icon-list) {
    color: #f5222d !important;
    font-size: 18px !important;
  }
}

.function-icon-wrapper {
  margin-bottom: 8px;
}

.function-text {
  font-size: 10px;
  color: #333;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

// 猜你喜欢区域
.recommend-section {
  margin: 0 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.section-more {
  font-size: 14px;
  color: #666;
  &:active {
    opacity: 0.7;
  }
}

// 角色切换区域（开发测试用）
.role-switch-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.current-role {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}
</style>
