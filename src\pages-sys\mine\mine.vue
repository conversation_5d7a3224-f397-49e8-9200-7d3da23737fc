<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '我的',
  },
}
</route>

<template>
  <view class="mine-container" :style="{ paddingTop: safeAreaInsetsTop + 'px' }">
    <!-- 页面内容 -->
    <view class="content">
      <text>系统管理员类用户 - 我的页面内容</text>

      <!-- 角色切换按钮组 -->
      <view class="role-switch-section">
        <text class="section-title">角色切换</text>
        <view class="button-group">
          <wd-button
            :type="globalRoleStore.getRole.valueOf() === 'user' ? 'primary' : 'default'"
            size="small"
            @click="switchRole(userTypeEnum.user)"
          >
            普通用户
          </wd-button>
          <wd-button
            :type="globalRoleStore.getRole.valueOf() === 'merchants' ? 'primary' : 'default'"
            size="small"
            @click="switchRole(userTypeEnum.merchants)"
          >
            商户用户
          </wd-button>
          <wd-button
            :type="globalRoleStore.getRole.valueOf() === 'system' ? 'primary' : 'default'"
            size="small"
            @click="switchRole(userTypeEnum.system)"
          >
            管理员
          </wd-button>
        </view>
        <text class="current-role">当前角色：{{ currentRole }}</text>
      </view>
    </view>
    <!-- 底部导航栏 -->
    <TabBar current-page="mine" />
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { userTypeEnum, useGlobalRole } from '../../store/globalRole'
import { useGlobalSafeArea } from '../../hooks/useSafeArea'

const globalRoleStore = useGlobalRole()

defineOptions({
  name: 'SysMine',
})

// 使用全局安全区域 Hook
const { safeAreaInsetsTop } = useGlobalSafeArea()

const currentRole = computed(() => {
  return getRoleText(globalRoleStore.getRole)
})

// 切换角色
const switchRole = (role: userTypeEnum) => {
  globalRoleStore.setRole(role)
  uni.showToast({
    title: `已切换到${getRoleText(role)}`,
    icon: 'success',
  })
}

// 获取角色显示文本
const getRoleText = (role: userTypeEnum) => {
  const roleMap = {
    user: '普通用户',
    merchants: '商户用户',
    system: '管理员',
  }
  return roleMap[role]
}
</script>

<style lang="scss" scoped>
.mine-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 30px;
}

.role-switch-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  width: 100%;
  max-width: 300px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.current-role {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}
</style>
