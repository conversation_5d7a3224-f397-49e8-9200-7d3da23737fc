<template>
  <wd-popup
    :model-value="visible"
    position="bottom"
    :safe-area-inset-bottom="false"
    :overlay="true"
    :overlay-style="{ backgroundColor: 'rgba(0,0,0,0.4)' }"
    :custom-style="`height: ${popupHeight}; max-height: ${popupHeight};`"
    @update:model-value="handleVisibleChange"
  >
    <view class="filter-popup">
      <view class="filter-header">
        <text class="filter-title">全部筛选</text>
        <view class="filter-actions">
          <!-- 显示模式切换 -->
          <view v-if="showViewModeSwitch" class="view-mode-switch">
            <view
              class="mode-btn"
              :class="{ active: viewMode === 'list' }"
              @click="handleViewModeChange('list')"
            >
              <text class="iconfont-sys iconsys-liebiao"></text>
            </view>
            <view
              class="mode-btn"
              :class="{ active: viewMode === 'grid' }"
              @click="handleViewModeChange('grid')"
            >
              <text class="iconfont-sys iconsys-more-grid-big"></text>
            </view>
          </view>
          <text class="close-btn" @click="handleClose">×</text>
        </view>
      </view>

      <!-- 滚动内容区域 -->
      <scroll-view class="filter-content" scroll-y>
        <!-- 价格区间 -->
        <view class="filter-section">
          <text class="section-title">价格区间</text>
          <view class="price-input-container">
            <view class="price-input-group">
              <wd-input
                :model-value="localFilters.priceMin"
                type="number"
                placeholder="最低价"
                size="large"
                @update:model-value="handlePriceMinChange"
              />
              <text class="price-separator">-</text>
              <wd-input
                :model-value="localFilters.priceMax"
                type="number"
                placeholder="最高价"
                size="large"
                @update:model-value="handlePriceMaxChange"
              />
            </view>
          </view>
        </view>

        <!-- 距离 -->
        <view class="filter-section">
          <text class="section-title">距离</text>
          <view class="option-card-grid">
            <view
              v-for="option in filterOptions.distance"
              :key="option.value"
              class="option-card"
              :class="{ active: localFilters.distance === option.value }"
              @click="handleDistanceChange(option.value)"
            >
              <text class="card-text">{{ option.label }}</text>
            </view>
          </view>
        </view>

        <!-- 新鲜度 -->
        <view class="filter-section">
          <text class="section-title">新鲜度</text>
          <view class="option-card-grid">
            <view
              v-for="option in filterOptions.freshness"
              :key="option.value"
              class="option-card"
              :class="{ active: localFilters.freshness === option.value }"
              @click="handleFreshnessChange(option.value)"
            >
              <text class="card-text">{{ option.label }}</text>
            </view>
          </view>
        </view>

        <!-- 底部按钮区域 -->
        <view class="filter-buttons">
          <view class="btn-clear" @click="handleReset">
            <text class="btn-text">清空选择</text>
          </view>
          <view class="btn-confirm" @click="handleApply">
            <text class="btn-text">确定</text>
          </view>
        </view>

        <!-- 底部安全区域占位 -->
        <view class="bottom-safe-area"></view>
      </scroll-view>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import type { FilterPopupProps, FilterPopupEmits, FilterValues } from './types'

defineOptions({
  name: 'FilterPopup',
})

const props = withDefaults(defineProps<FilterPopupProps>(), {
  visible: false,
  viewMode: 'grid',
  showViewModeSwitch: true,
})

const emit = defineEmits<FilterPopupEmits>()

// 动态计算弹窗高度
const popupHeight = ref('60vh')

const calculatePopupHeight = () => {
  const systemInfo = uni.getSystemInfoSync()
  const screenHeight = systemInfo.screenHeight
  const safeAreaBottom = systemInfo.safeAreaInsets?.bottom || 0
  const tabBarHeight = 80 // 底部导航栏实际很高，调整为80px
  const buttonAreaHeight = 80 // 按钮区域高度
  const headerHeight = 60 // 弹窗头部高度
  
  // 计算可用高度，预留底部安全区域、导航栏和按钮区域空间
  const reservedHeight = safeAreaBottom + tabBarHeight + buttonAreaHeight + headerHeight + 20
  const availableHeight = screenHeight - reservedHeight
  const maxHeight = Math.min(availableHeight, screenHeight * 0.65) // 调整为65%最大高度
  
  popupHeight.value = `${Math.max(maxHeight, 300)}px` // 确保最小高度300px
  
  console.log('筛选弹窗系统信息:', {
    screenHeight,
    safeAreaBottom,
    tabBarHeight,
    buttonAreaHeight,
    reservedHeight,
    maxHeight,
    finalHeight: popupHeight.value,
    platform: systemInfo.platform
  })
}

// 本地筛选条件
const localFilters = ref<FilterValues>({ ...props.currentFilters })

// 监听props变化，同步本地状态
watch(() => props.currentFilters, (newFilters) => {
  localFilters.value = { ...newFilters }
}, { deep: true })

// 监听visible变化，重新计算弹窗高度
watch(() => props.visible, (visible) => {
  if (visible) {
    calculatePopupHeight()
  }
})

// 处理弹窗显示状态变化
const handleVisibleChange = (visible: boolean) => {
  emit('update:visible', visible)
}

// 处理关闭
const handleClose = () => {
  emit('update:visible', false)
}

// 处理视图模式变化
const handleViewModeChange = (mode: 'list' | 'grid') => {
  emit('update:viewMode', mode)
}

// 处理价格变化
const handlePriceMinChange = (value: string) => {
  localFilters.value.priceMin = value
  emit('update:currentFilters', { ...localFilters.value })
}

const handlePriceMaxChange = (value: string) => {
  localFilters.value.priceMax = value
  emit('update:currentFilters', { ...localFilters.value })
}

// 处理距离变化
const handleDistanceChange = (value: string) => {
  localFilters.value.distance = value
  emit('update:currentFilters', { ...localFilters.value })
}

// 处理新鲜度变化
const handleFreshnessChange = (value: string) => {
  localFilters.value.freshness = value
  emit('update:currentFilters', { ...localFilters.value })
}

// 处理重置
const handleReset = () => {
  emit('reset')
}

// 处理应用筛选
const handleApply = () => {
  emit('apply', { ...localFilters.value })
}

// 初始化时计算弹窗高度
calculatePopupHeight()
</script>

<style lang="scss" scoped>
// 筛选弹窗样式
.filter-popup {
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 10px 6px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  flex-shrink: 0;
  box-sizing: border-box;
  height: 60px;
}

.filter-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.filter-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.view-mode-switch {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 6px;
  padding: 2px;
  gap: 2px;
}

.mode-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;

  .iconfont-sys {
    font-size: 16px;
    color: #666;
  }

  &.active {
    background: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .iconfont-sys {
      color: #1890ff;
    }
  }

  &:active {
    transform: scale(0.95);
  }
}

.close-btn {
  font-size: 24px;
  color: #999;
  padding: 4px;
  line-height: 1;

  &:active {
    opacity: 0.7;
  }
}

.filter-content {
  flex: 1;
  padding: 16px 16px 16px 16px;
  background: #fff;
  overflow-y: auto;
  box-sizing: border-box;
}

.filter-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: block;
}

.option-card-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.option-card {
  padding: 12px 8px;
  background: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  transition: all 0.2s;
  text-align: center;
  box-sizing: border-box;
  width: 100%;

  &.active {
    background: #fff2e6;
    border-color: #ffb366;
  }

  &:active {
    transform: scale(0.95);
  }
}

.card-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.price-input-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 12px;
}

.price-input-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.price-separator {
  font-size: 16px;
  color: #666;
}

.filter-buttons {
  display: flex;
  gap: 12px;
  padding: 20px 16px 16px 16px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
}

.btn-clear,
.btn-confirm {
  flex: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  border: none;
  transition: all 0.2s;

  &:active {
    transform: scale(0.98);
    opacity: 0.8;
  }
}

.btn-clear {
  background: #f5f5f5;
  border: 1px solid #e0e0e0;

  .btn-text {
    color: #666;
  }
}

.btn-confirm {
  background: #ff6b35;

  .btn-text {
    color: #fff;
  }
}

.btn-text {
  font-size: 16px;
  font-weight: 500;
}

.bottom-safe-area {
  height: 60px;
}
</style> 