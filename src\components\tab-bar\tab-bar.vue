<template>
  <wd-tabbar
    v-model="tabbarStore.curIdx"
    @change="selectTabBar"
    fixed
    placeholder
    safeAreaInsetBottom
    bordered
    active-color="#007aff"
    inactive-color="#999999"
  >
    <wd-tabbar-item v-for="(item, idx) in tabbarList" :key="item.path" :title="item.text">
      <template #icon>
        <!-- 使用wot组件图标 - 没有特殊前缀的图标 -->
        <wd-icon
          v-if="!item.icon.startsWith('iconsys')"
          :name="item.icon"
          size="20px"
          :color="idx === tabbarStore.curIdx ? '#007aff' : '#999999'"
        />
        <!-- 使用自定义图标 - iconsys开头的图标 -->
        <view
          v-else
          class="iconfont-sys"
          :class="`${item.icon}`"
          :style="{
            fontSize: '20px',
            color: idx === tabbarStore.curIdx ? '#007aff' : '#999999',
          }"
        ></view>
      </template>
    </wd-tabbar-item>
  </wd-tabbar>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { tabbarList as _tabBarList } from './tabbarList'
import { tabbarStore } from './tabbar'

defineOptions({
  name: 'TabBar',
})

/** tabbarList 里面的 path 从 pages.config.ts 得到 */
const tabbarList = _tabBarList.map((item) => ({ ...item, path: `/${item.pagePath}` }))

function selectTabBar({ value: index }: { value: number }) {
  const url = tabbarList[index].path
  tabbarStore.setCurIdx(index)

  console.log('Tab点击，索引:', index, '路径:', url)

  // 统一使用 switchTab 进行跳转
  uni.switchTab({
    url,
    success: () => {
      console.log('switchTab 跳转成功:', url)
    },
    fail: (err) => {
      console.log('switchTab 跳转失败:', err)
    },
  })
}

onMounted(() => {
  console.log('TabBar组件挂载完成')
  console.log('当前索引:', tabbarStore.curIdx)
  console.log('TabBar配置:', tabbarList)

  // 根据当前页面路径自动设置正确的索引
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const currentRoute = (currentPage as any).route || (currentPage as any).__route__

  if (currentRoute) {
    const currentPath = `/${currentRoute}`
    const currentIndex = tabbarList.findIndex((item) => item.path === currentPath)

    if (currentIndex !== -1 && currentIndex !== tabbarStore.curIdx) {
      console.log('自动设置 TabBar 索引:', currentIndex, '路径:', currentPath)
      tabbarStore.setCurIdx(currentIndex)
    }
  }
})
</script>

<style scoped></style>
