<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '用户表单',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="user-form-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      :title="isEditMode ? '编辑用户' : '新增用户'"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <view class="main-layout">
        <view class="user-form">
          <wd-form ref="formRef" :model="userForm" :rules="formRules" errorType="message">
            <!-- 基本信息 -->
            <view class="form-section">
              <view class="section-title">基本信息</view>

              <!-- 头像上传 -->
              <view class="avatar-section">
                <text class="avatar-label">头像</text>
                <view class="avatar-upload">
                  <view class="avatar-container" @click="chooseAvatar">
                    <wd-img
                      v-if="userForm.avatar"
                      :src="userForm.avatar"
                      width="80px"
                      height="80px"
                      round
                      mode="aspectFill"
                      custom-class="avatar-image"
                      @error="handleAvatarError"
                      @load="handleAvatarLoad"
                    />
                    <view v-else class="avatar-placeholder">
                      <wd-icon name="camera" size="24" color="#999" />
                      <text class="placeholder-text">点击上传头像</text>
                    </view>
                    <!-- 上传状态指示器 -->
                    <view v-if="uploadLoading" class="upload-overlay">
                      <view class="upload-loading">
                        <wd-loading />
                        <text class="loading-text">上传中...</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>

              <wd-cell-group border>
                <!-- 用户名 -->
                <wd-input
                  label="用户名"
                  label-width="80px"
                  prop="username"
                  v-model="userForm.username"
                  placeholder="请输入用户名"
                  :disabled="isEditMode"
                  :maxlength="20"
                  show-word-limit
                  clearable
                  :rules="formRules.username"
                  @blur="checkUsernameExists"
                />

                <!-- 密码 - 仅新增时显示 -->
                <wd-input
                  v-if="!isEditMode"
                  label="密码"
                  label-width="80px"
                  prop="password"
                  v-model="userForm.password"
                  show-password
                  placeholder="请输入密码"
                  :maxlength="30"
                  show-word-limit
                  clearable
                  :rules="formRules.password"
                />

                <!-- 真实姓名 -->
                <wd-input
                  label="真实姓名"
                  label-width="80px"
                  prop="real_name"
                  v-model="userForm.real_name"
                  placeholder="请输入真实姓名"
                  :maxlength="10"
                  show-word-limit
                  clearable
                  :rules="formRules.real_name"
                />

                <!-- 性别 -->
                <wd-picker
                  label="性别"
                  label-width="80px"
                  prop="gender"
                  v-model="userForm.gender"
                  :columns="genderOptions"
                  placeholder="请选择性别"
                  @confirm="onGenderChange"
                />
              </wd-cell-group>
            </view>

            <!-- 联系信息 -->
            <view class="form-section">
              <view class="section-title">联系信息</view>

              <wd-cell-group border>
                <!-- 手机号 -->
                <wd-input
                  label="手机号"
                  label-width="80px"
                  prop="phone"
                  v-model="userForm.phone"
                  placeholder="请输入手机号"
                  :maxlength="11"
                  clearable
                  :rules="formRules.phone"
                  @blur="checkPhoneExists"
                />

                <!-- 邮箱 -->
                <wd-input
                  label="邮箱"
                  label-width="80px"
                  prop="email"
                  v-model="userForm.email"
                  placeholder="请输入邮箱"
                  clearable
                  :rules="formRules.email"
                  @blur="checkEmailExists"
                />
              </wd-cell-group>
            </view>

            <!-- 账户设置 -->
            <view class="form-section">
              <view class="section-title">账户设置</view>

              <wd-cell-group border>
                <!-- 状态 -->
                <wd-picker
                  label="状态"
                  label-width="80px"
                  prop="status"
                  v-model="userForm.status"
                  :columns="statusOptions"
                  placeholder="请选择状态"
                  :rules="formRules.status"
                  @confirm="onStatusChange"
                />

                <!-- 角色 -->
                <wd-select-picker
                  label="角色"
                  label-width="80px"
                  prop="role_ids"
                  v-model="userForm.role_ids"
                  :columns="roleOptions"
                  type="checkbox"
                  placeholder="请选择角色"
                  @confirm="onRoleChange"
                />
              </wd-cell-group>
            </view>

            <!-- 其他信息 -->
            <view class="form-section">
              <view class="section-title">其他信息</view>

              <wd-cell-group border>
                <!-- 备注 -->
                <wd-textarea
                  label="备注"
                  label-width="80px"
                  prop="remark"
                  v-model="userForm.remark"
                  placeholder="请输入备注信息"
                  :maxlength="200"
                  show-word-limit
                  :rows="3"
                  auto-height
                />
              </wd-cell-group>
            </view>
          </wd-form>

          <!-- 操作按钮 -->
          <view class="form-actions">
            <wd-button @click="handleCancel" size="large" custom-class="cancel-btn">取消</wd-button>
            <wd-button
              type="primary"
              @click="handleSubmit"
              size="large"
              :loading="submitting"
              custom-class="submit-btn"
            >
              {{ isEditMode ? '更新' : '创建' }}
            </wd-button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {
  createSystemUserApi,
  updateSystemUserApi,
  getSystemUserDetailApi,
  checkUsernameExistsApi,
  checkEmailExistsApi,
  checkPhoneExistsApi,
  type ISysUserCreateRequest,
  type ISysUserUpdateRequest,
  type ISysUserDetailResponse,
} from '@/api/sys/systemUserApi'
import { getSystemRoleSimpleApi, type ISysRoleSimpleResponse } from '@/api/sys/systemRoleApi'
import useUpload from '@/hooks/useUpload'

defineOptions({
  name: 'UserForm',
})

// 头像上传
const {
  loading: uploadLoading,
  error: uploadError,
  data: uploadData,
  run: startUpload,
} = useUpload<string>()

// 页面参数
const pageParams = ref<{
  mode: 'add' | 'edit'
  userId?: string
}>({
  mode: 'add',
})

// 计算属性
const isEditMode = computed(() => pageParams.value.mode === 'edit')

// 表单引用
const formRef = ref()

// 提交状态
const submitting = ref(false)

// 用户表单数据
const userForm = reactive<ISysUserCreateRequest & { id?: string }>({
  username: '',
  password: '',
  real_name: '',
  phone: '',
  email: '',
  avatar: '',
  gender: undefined,
  status: 1, // 默认启用
  role_ids: [],
  remark: '',
})

// 性别选项
const genderOptions = ref([
  { label: '男', value: 1 },
  { label: '女', value: 2 },
  { label: '未知', value: 3 },
])

// 状态选项
const statusOptions = ref([
  { label: '启用', value: 1 },
  { label: '禁用', value: 2 },
  { label: '锁定', value: 3 },
])

// 角色选项 - 动态加载
const roleOptions = ref<{ label: string; value: string }[]>([])

// 获取角色数据
const loadRoleOptions = async () => {
  try {
    const result = await getSystemRoleSimpleApi()

    roleOptions.value = result.data.map((role: ISysRoleSimpleResponse) => ({
      label: role.name,
      value: role.id,
    }))

    console.log('角色数据加载成功:', roleOptions.value)
  } catch (error) {
    console.error('加载角色数据失败:', error)
    uni.showToast({
      title: '加载角色数据失败',
      icon: 'none',
    })
  }
}

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名' },
    {
      pattern: /^[a-zA-Z0-9_]{3,20}$/,
      message: '用户名只能包含字母、数字、下划线，长度3-20位',
    },
  ],
  password: [
    { required: true, message: '请输入密码' },
    { min: 6, max: 30, message: '密码长度必须在6-30个字符之间' },
    {
      pattern: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,30}$/,
      message: '密码必须包含字母和数字，长度6-30位',
    },
  ],
  real_name: [
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z\s]{1,10}$/,
      message: '真实姓名只能包含中英文，长度1-10位',
    },
  ],
  phone: [
    { required: true, message: '请输入手机号' },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '手机号长度必须为11位且格式正确',
    },
  ],
  email: [
    { required: true, message: '请输入邮箱' },
    {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: '邮箱格式不正确',
    },
  ],
  status: [{ required: true, message: '请选择状态' }],
} as any

// 检查用户名是否存在
const checkUsernameExists = async () => {
  if (!userForm.username || userForm.username.length < 3) return

  try {
    const params = {
      username: userForm.username,
      exclude_id: isEditMode.value ? pageParams.value.userId : undefined,
    }
    const result = await checkUsernameExistsApi(params)
    if (result.data) {
      uni.showToast({
        title: '用户名已存在',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('检查用户名失败:', error)
  }
}

// 检查邮箱是否存在
const checkEmailExists = async () => {
  // 先检查是否为空（必填验证）
  if (!userForm.email || !userForm.email.trim()) {
    uni.showToast({
      title: '请输入邮箱',
      icon: 'none',
    })
    return
  }

  // 检查邮箱格式
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailPattern.test(userForm.email)) {
    uni.showToast({
      title: '邮箱格式不正确',
      icon: 'none',
    })
    return
  }

  try {
    const params = {
      email: userForm.email,
      exclude_id: isEditMode.value ? pageParams.value.userId : undefined,
    }
    const result = await checkEmailExistsApi(params)
    if (result.data) {
      uni.showToast({
        title: '邮箱已存在',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('检查邮箱失败:', error)
  }
}

// 检查手机号是否存在
const checkPhoneExists = async () => {
  // 先检查是否为空（必填验证）
  if (!userForm.phone || !userForm.phone.trim()) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none',
    })
    return
  }

  // 检查手机号格式
  const phonePattern = /^1[3-9]\d{9}$/
  if (!phonePattern.test(userForm.phone)) {
    uni.showToast({
      title: '手机号长度必须为11位且格式正确',
      icon: 'none',
    })
    return
  }

  try {
    const params = {
      phone: userForm.phone,
      exclude_id: isEditMode.value ? pageParams.value.userId : undefined,
    }
    const result = await checkPhoneExistsApi(params)
    if (result.data) {
      uni.showToast({
        title: '手机号已存在',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('检查手机号失败:', error)
  }
}

// 性别选择事件
const onGenderChange = ({ value }: { value: number }) => {
  userForm.gender = value
}

// 状态选择事件
const onStatusChange = ({ value }: { value: number }) => {
  userForm.status = value
}

// 角色选择事件
const onRoleChange = ({ value }: { value: string[] }) => {
  console.log('角色选择变化:', value)
  // 直接使用选中的值，不需要中间变量
  userForm.role_ids = value || []
}

// 头像上传功能
const chooseAvatar = () => {
  uni.showModal({
    title: '更换头像',
    content: '是否要更换头像？',
    success: (res) => {
      if (res.confirm) {
        // 使用新的参数方式调用
        startUpload('/sys-user/avatar', 'avatar')
      }
    },
  })
}

// 监听上传结果
watch(
  () => uploadData.value,
  (newData) => {
    if (newData) {
      try {
        // 解析上传返回的数据
        const uploadResult = JSON.parse(newData)

        // 检查返回结果的格式
        if (uploadResult.code === 200 && uploadResult.data && uploadResult.data.file_url) {
          // 使用 file_url 作为头像地址
          userForm.avatar = uploadResult.data.file_url

          uni.showToast({
            title: uploadResult.message,
            icon: 'success',
          })
        } else {
          throw new Error(uploadResult.message || '上传失败')
        }
      } catch (e) {
        console.error('解析上传结果失败:', e)
        uni.showToast({
          title: '头像上传失败' + (e instanceof Error ? `: ${e.message}` : ''),
          icon: 'none',
        })
      }
    }
  },
)

// 监听上传错误
watch(
  () => uploadError.value,
  (hasError) => {
    if (hasError) {
      uni.showToast({
        title: '头像上传失败',
        icon: 'none',
      })
    }
  },
)

// 加载用户详情
const loadUserDetail = async (userId: string) => {
  try {
    uni.showLoading({ title: '加载中...' })

    const result = await getSystemUserDetailApi(userId)
    const userDetail = result.data

    // 确保role_ids为数组格式
    const roleIds = userDetail.roles?.map((role) => role.id) || []
    console.log(
      '加载用户详情 - 角色IDs:',
      roleIds,
      '类型:',
      typeof roleIds,
      '是否为数组:',
      Array.isArray(roleIds),
    )

    // 填充表单数据
    Object.assign(userForm, {
      id: userDetail.id,
      username: userDetail.username,
      real_name: userDetail.real_name || '',
      phone: userDetail.phone || '',
      email: userDetail.email || '',
      avatar: userDetail.avatar || '',
      gender: userDetail.gender,
      status: userDetail.status,
      role_ids: roleIds,
      remark: userDetail.remark || '',
    })

    console.log('用户详情加载成功:', userDetail)
    console.log('表单role_ids:', userForm.role_ids)
  } catch (error) {
    console.error('加载用户详情失败:', error)
    uni.showToast({
      title: '加载用户详情失败',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 验证表单 - 按照Wot UI官方文档正确处理
    const { valid, errors } = await formRef.value.validate()

    console.log('表单验证结果:', { valid, errors })

    if (!valid) {
      console.log('表单验证失败:', errors)
      uni.showToast({
        title: '请检查表单信息',
        icon: 'none',
      })
      return
    }

    console.log('表单验证通过，开始提交数据')
    submitting.value = true

    // 确保role_ids为数组格式
    const role_ids = Array.isArray(userForm.role_ids) ? userForm.role_ids : []
    console.log(
      '提交前role_ids:',
      role_ids,
      '类型:',
      typeof role_ids,
      '是否为数组:',
      Array.isArray(role_ids),
    )

    if (isEditMode.value) {
      // 编辑模式
      const updateData: ISysUserUpdateRequest = {
        real_name: userForm.real_name,
        phone: userForm.phone,
        email: userForm.email,
        avatar: userForm.avatar,
        gender: userForm.gender,
        status: userForm.status,
        role_ids: role_ids,
        remark: userForm.remark,
      }

      console.log('编辑提交数据:', updateData)
      await updateSystemUserApi(pageParams.value.userId!, updateData)

      uni.showToast({
        title: '更新成功',
        icon: 'success',
      })
    } else {
      // 新增模式
      const createData: ISysUserCreateRequest = {
        username: userForm.username,
        password: userForm.password,
        real_name: userForm.real_name,
        phone: userForm.phone,
        email: userForm.email,
        avatar: userForm.avatar,
        gender: userForm.gender,
        status: userForm.status,
        role_ids: role_ids,
        remark: userForm.remark,
      }

      console.log('新增提交数据:', createData)
      await createSystemUserApi(createData)

      uni.showToast({
        title: '创建成功',
        icon: 'success',
      })
    }

    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1000)
  } catch (error) {
    console.error('操作失败:', error)

    // 如果是验证错误，显示验证失败的提示
    if (error && typeof error === 'object' && 'errors' in error) {
      console.log('表单验证异常:', error)
      uni.showToast({
        title: '表单验证失败',
        icon: 'none',
      })
    } else {
      // 其他错误（如API调用失败）
      uni.showToast({
        title: isEditMode.value ? '更新失败' : '创建失败',
        icon: 'none',
      })
    }
  } finally {
    submitting.value = false
  }
}

// 取消操作
const handleCancel = () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消操作吗？未保存的内容将丢失。',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack()
      }
    },
  })
}

// 返回功能
const handleBack = () => {
  handleCancel()
}

// 页面加载事件
onLoad((options) => {
  console.log('用户表单页面参数:', options)

  if (options) {
    pageParams.value.mode = (options.mode as 'add' | 'edit') || 'add'
    pageParams.value.userId = options.userId
  }

  // 如果是编辑模式，加载用户详情
  if (isEditMode.value && pageParams.value.userId) {
    loadUserDetail(pageParams.value.userId)
  }
})

onMounted(() => {
  console.log('用户表单页面挂载完成')
  console.log('页面模式:', pageParams.value.mode)
  console.log('用户ID:', pageParams.value.userId)

  // 加载角色数据
  loadRoleOptions()

  // 确保role_ids始终是数组类型
  if (!Array.isArray(userForm.role_ids)) {
    console.warn('role_ids不是数组，强制转换为数组:', userForm.role_ids)
    userForm.role_ids = []
  }
  console.log(
    '初始化后的role_ids:',
    userForm.role_ids,
    '是否为数组:',
    Array.isArray(userForm.role_ids),
  )
})

// 图片加载处理函数
const handleAvatarLoad = (event: any) => {
  console.log('头像加载成功:', event)
}

const handleAvatarError = (event: any) => {
  console.error('头像加载失败:', event)
  uni.showToast({
    title: '头像加载失败',
    icon: 'none',
  })
}
</script>

<style lang="scss" scoped>
.user-form-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.main-layout {
  padding: 12px;
}

.user-form {
  // 简化表单样式，直接展示
}

.form-section {
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 15px 12px 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

// 头像区域样式
.avatar-section {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: white;
  margin-bottom: 12px;
  border-radius: 8px;
}

.avatar-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  width: 80px;
  text-align: right;
  padding-right: 12px;
}

.avatar-upload {
  flex: 1;
}

.avatar-container {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  border: 2px dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  transition: border-color 0.3s;

  &:hover {
    border-color: #4285f4;
  }
}

// wd-img 组件样式重写
:deep(.avatar-image) {
  border-radius: 8px;
  border: none !important;
}

.avatar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.placeholder-text {
  font-size: 12px;
  color: #999;
  text-align: center;
}

// 上传状态指示器
.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-text {
  font-size: 12px;
  color: white;
  font-weight: 500;
}

.form-actions {
  display: flex;
  gap: 12px;
  padding: 12px;
}

:deep(.cancel-btn) {
  flex: 1;
  background-color: #f5f5f5 !important;
  color: #666 !important;
  border-color: #ddd !important;
}

:deep(.submit-btn) {
  flex: 1;
}

// Wot UI 表单项样式优化
:deep(.wd-cell-group) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.wd-cell) {
  background-color: white;
}

:deep(.wd-input) {
  background-color: transparent;
}

:deep(.wd-picker) {
  background-color: transparent;
}

:deep(.wd-textarea) {
  background-color: transparent;
}
</style>
