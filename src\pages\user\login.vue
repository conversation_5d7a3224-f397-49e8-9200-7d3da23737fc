<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '登录',
  },
  access: {
    requireAuth: false,
  },
}
</route>

<template>
  <view class="login-page">
    <!-- 使用自定义导航栏 -->
    <Navbar title="用户登录" :fixed="true" :placeholder="true" @back="goBack" />

    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 切换登录方式 -->
      <LoginTabs
        v-model="loginType"
        :tabs="[
          { label: '密码登录', value: 'password' },
          { label: '手机号登录', value: 'sms' },
        ]"
      />

      <!-- 登录表单 -->
      <view class="form-content">
        <!-- 密码登录 -->
        <block v-if="loginType === 'password'">
          <LoginInput
            v-model="loginForm.username"
            placeholder="请输入用户名/手机号"
            :clearable="true"
            :maxlength="50"
          />
          <LoginInput
            v-model="loginForm.password"
            show-password
            placeholder="请输入密码"
            :clearable="true"
            :maxlength="20"
          />
        </block>

        <!-- 手机号登录 -->
        <block v-else>
          <LoginInput
            v-model="smsForm.phone"
            type="number"
            placeholder="请输入手机号"
            :clearable="true"
            :maxlength="11"
          />
          <CodeInput
            v-model="smsForm.code"
            :can-send="canSendCode"
            :countdown="countdown"
            @send="sendSmsCode"
          />
        </block>

        <!-- 协议同意 -->
        <AgreementCheckbox
          v-model="agreeTerms"
          @show-agreement="showUserAgreement"
          @show-privacy="showPrivacyPolicy"
        />

        <!-- 登录按钮 -->
        <wd-button
          type="primary"
          size="large"
          :loading="loginLoading"
          :disabled="!canLogin"
          @click="handleLogin"
          custom-class="login-btn"
        >
          {{ loginLoading ? '登录中...' : agreeTerms ? '登录' : '同意协议并登录' }}
        </wd-button>

        <!-- 忘记密码和注册 -->
        <view class="form-footer">
          <text class="link-text" @click="goToForgotPassword">忘记密码？</text>
          <text class="link-text" @click="goToRegister">注册账号</text>
        </view>
      </view>

      <!-- 其他登录方式 -->
      <OtherLogin
        @wechat-login="handleWechatLogin"
        @get-phone-number="handleGetPhoneNumber"
        @switch-sms-login="switchToSmsLogin"
      />
    </view>

    <!-- 协议弹窗 -->
    <AgreementPopup
      v-model="showAgreementPopup"
      :title="agreementTitle"
      :content="agreementContent"
    />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import WxUtil from '../../utils/wxUtil'
import PLATFORM from '../../utils/platform'
import type { UserInfo } from '../../utils/wxUtil'

// 登录方式
const loginType = ref<'password' | 'sms'>('password')

// 表单数据
const loginForm = ref({
  username: '',
  password: '',
})

const smsForm = ref({
  phone: '',
  code: '',
})

// 状态管理
const loginLoading = ref(false)
const wechatLoginLoading = ref(false)
const countdown = ref(0)
let countdownTimer: any = null

// 协议弹窗
const showAgreementPopup = ref(false)
const agreementTitle = ref('')
const agreementContent = ref('')

// 协议同意
const agreeTerms = ref(false)

// 计算属性
const canLogin = computed(() => {
  if (loginType.value === 'password') {
    return loginForm.value.username.trim() && loginForm.value.password.trim()
  } else {
    return smsForm.value.phone.trim() && smsForm.value.code.trim()
  }
})

const canSendCode = computed(() => {
  return /^1[3-9]\d{9}$/.test(smsForm.value.phone)
})

// 发送验证码
const sendSmsCode = async () => {
  if (!canSendCode.value) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none',
    })
    return
  }

  try {
    // TODO: 调用发送验证码API
    console.log('发送验证码到:', smsForm.value.phone)

    // 模拟发送成功
    uni.showToast({
      title: '验证码已发送',
      icon: 'success',
    })

    // 开始倒计时
    countdown.value = 60
    countdownTimer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(countdownTimer)
      }
    }, 1000)
  } catch (error: any) {
    console.error('发送验证码失败:', error)
    uni.showToast({
      title: error.message || '发送失败',
      icon: 'none',
    })
  }
}

// 页面方法
const handleLogin = async () => {
  if (!canLogin.value) {
    uni.showToast({
      title: '请完善登录信息',
      icon: 'none',
    })
    return
  }

  // 如果用户没有同意协议，点击按钮时自动同意
  if (!agreeTerms.value) {
    agreeTerms.value = true
  }

  loginLoading.value = true
  try {
    if (loginType.value === 'password') {
      // TODO: 调用密码登录API
      console.log('密码登录信息:', loginForm.value)
    } else {
      // TODO: 调用手机号登录API
      console.log('手机号登录信息:', smsForm.value)
    }

    // 模拟登录延迟
    await new Promise((resolve) => setTimeout(resolve, 1500))

    // 模拟登录成功
    uni.showToast({
      title: '登录成功',
      icon: 'success',
    })

    // 登录成功后跳转
    setTimeout(() => {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const options = (currentPage as any).options

      if (options && options.redirect) {
        uni.redirectTo({
          url: decodeURIComponent(options.redirect),
        })
      } else {
        uni.reLaunch({
          url: '/pages/user/index',
        })
      }
    }, 1000)
  } catch (error: any) {
    console.error('登录失败:', error)
    
    // 获取错误信息
    let errorMessage = '登录失败'
    if (error instanceof Error) {
      errorMessage = error.message
    } else if (typeof error === 'string') {
      errorMessage = error
    }
    
    // 显示错误信息
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000, // 延长显示时间到3秒
    })
  } finally {
    loginLoading.value = false
  }
}

// 微信登录
const handleWechatLogin = async () => {
  // 微信登录需要用户主动同意协议
  if (!agreeTerms.value) {
    uni.showToast({
      title: '请先同意用户协议',
      icon: 'none',
    })
    return
  }

  wechatLoginLoading.value = true
  try {
    console.log('开始微信登录')
    let wxLoginCode = ''

    // 微信小程序环境
    if (PLATFORM.isMpWeixin) {
      console.log('微信小程序登录')
      // 使用WxUtil工具类处理微信登录
      const mpLoginResult = await WxUtil.login()
      console.log('获取到微信登录code:', mpLoginResult.code)

      // 保存code到本地，用于信息完善页面使用
      wxLoginCode = mpLoginResult.code
    }
    // APP环境
    else if (PLATFORM.isApp) {
      console.log('APP端微信登录')
      // 检查是否安装微信
      const isWXAppInstalled = await new Promise<boolean>((resolve) => {
        // 仅在Android/iOS上检查
        if (
          uni.getSystemInfoSync().platform === 'android' ||
          uni.getSystemInfoSync().platform === 'ios'
        ) {
          plus.oauth.getServices(
            (services) => {
              const wxService = services.find((s) => s.id === 'weixin')
              if (wxService) {
                resolve(true)
              } else {
                resolve(false)
              }
            },
            (err) => {
              console.error('获取登录服务列表失败:', err)
              resolve(false)
            },
          )
        } else {
          resolve(false)
        }
      })

      if (!isWXAppInstalled) {
        throw new Error('未安装微信或不支持微信登录')
      }

      // 使用WxUtil工具类处理微信登录
      const appLoginResult = await WxUtil.login()
      console.log('获取到微信登录code:', appLoginResult.code)

      // 保存code到本地，用于信息完善页面使用
      wxLoginCode = appLoginResult.code
    }
    // H5环境
    else {
      console.log('H5微信登录')
      uni.showToast({
        title: 'H5环境暂不支持微信登录',
        icon: 'none',
        duration: 2000,
      })
      wechatLoginLoading.value = false
      return
    }

    // 临时模拟登录成功（后端接口实现后删除此部分）
    console.log('模拟微信登录检查...')

    // 模拟延迟
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 需要完善信息
    const needSetupProfile = true

    if (needSetupProfile) {
      console.log('需要完善用户信息')
      // 跳转到信息完善页面
      const redirect = getCurrentRedirect()
      uni.navigateTo({
        url: `/pages/user/profile-setup?redirect=${encodeURIComponent(redirect)}`,
      })
    } else {
      console.log('用户信息已完善，直接登录')
      // 创建用户信息对象
      const userInfo = WxUtil.createUserInfo(
        '微信用户' + Math.floor(Math.random() * 1000),
        'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
        'mock_openid_' + Date.now(),
      )

      // 模拟保存token

      uni.showToast({
        title: '微信登录成功',
        icon: 'success',
      })

      // 登录成功后跳转，将用户信息作为参数传递
      redirectAfterLogin(userInfo)
    }
  } catch (error: any) {
    console.error('微信登录失败:', error)

    // 使用WxUtil处理登录错误
    const errorMessage = error.message || '微信登录失败'

    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000,
    })
  } finally {
    wechatLoginLoading.value = false
  }
}

// 获取当前重定向地址
const getCurrentRedirect = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = (currentPage as any).options

  if (options && options.redirect) {
    return decodeURIComponent(options.redirect)
  } else {
    return '/pages/user/index'
  }
}

// 登录成功后重定向
const redirectAfterLogin = (userInfo?: UserInfo) => {
  setTimeout(() => {
    const redirect = getCurrentRedirect()

    // 如果有用户信息，将其作为参数传递
    const userInfoParam = userInfo
      ? `?userInfo=${encodeURIComponent(JSON.stringify(userInfo))}`
      : ''

    if (redirect.startsWith('/pages/user/login')) {
      // 避免重定向到登录页面
      uni.reLaunch({
        url: `/pages/user/index${userInfoParam}`,
      })
    } else {
      // 添加用户信息参数
      const redirectUrl =
        redirect +
        (redirect.includes('?') ? '&' : '?') +
        (userInfoParam ? userInfoParam.substring(1) : '')

      uni.redirectTo({
        url: redirectUrl,
        fail: () => {
          // 如果redirectTo失败，尝试reLaunch
          uni.reLaunch({
            url: `/pages/user/index${userInfoParam}`,
          })
        },
      })
    }
  }, 1000)
}

// 协议相关
const showUserAgreement = () => {
  agreementTitle.value = '用户协议'
  agreementContent.value = `测试用户协议内容

这是测试环境的用户协议。

1. 测试条款一
2. 测试条款二
3. 测试条款三

仅供测试使用。`
  showAgreementPopup.value = true
}

const showPrivacyPolicy = () => {
  agreementTitle.value = '隐私政策'
  agreementContent.value = `测试隐私政策内容

这是测试环境的隐私政策。

1. 测试隐私条款一
2. 测试隐私条款二
3. 测试隐私条款三

仅供测试使用。`
  showAgreementPopup.value = true
}

// 其他页面跳转
const goToForgotPassword = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none',
  })
}

const goToRegister = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none',
  })
}

// 手机号快捷登录
const handleQuickPhoneLogin = async () => {
  // 微信手机号登录需要用户主动同意协议
  if (!agreeTerms.value) {
    uni.showToast({
      title: '请先同意用户协议',
      icon: 'none',
    })
    return
  }

  // 只在微信小程序环境下支持
  if (!PLATFORM.isMpWeixin) {
    uni.showToast({
      title: '仅微信小程序支持手机号快捷登录',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  try {
    console.log('开始微信手机号快捷登录')

    // 先获取微信登录code
    const loginResult = await WxUtil.login()
    const wxLoginCode = loginResult.code
    console.log('获取到微信登录code:', wxLoginCode)

    // 调用微信获取手机号的按钮组件
    // 注意：这个功能需要在页面中使用button组件，open-type="getPhoneNumber"
    // 由于我们使用的是自定义UI，需要创建一个隐藏的button来触发

    // 创建并触发隐藏的获取手机号按钮
    const hiddenPhoneBtn = uni.createSelectorQuery().select('#hiddenPhoneBtn')
    if (hiddenPhoneBtn) {
      // 触发点击事件
      hiddenPhoneBtn.node((res) => {
        if (res && res[0]) {
          res[0].click()
        }
      })
    } else {
      // 如果找不到按钮，提示用户
      uni.showToast({
        title: '请稍后重试',
        icon: 'none',
      })
    }
  } catch (error: any) {
    console.error('微信手机号登录失败:', error)
    uni.showToast({
      title: error.message || '获取手机号失败',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 处理微信获取手机号回调
const handleGetPhoneNumber = async (e: any) => {
  console.log('微信获取手机号回调:', e)

  if (e.detail.errMsg === 'getPhoneNumber:ok') {
    // 获取到加密的手机号信息
    const { encryptedData, iv, code } = e.detail

    try {
      // TODO: 将加密数据发送到后端解密获取手机号
      console.log('获取到的加密数据:', {
        encryptedData,
        iv,
        code,
      })

      // 获取之前保存的登录code
      const wxLoginCode = '' // 需要从其他地方获取

      // 模拟后端解密并登录
      uni.showLoading({
        title: '登录中...',
      })

      await new Promise((resolve) => setTimeout(resolve, 1500))

      // TODO: 实际项目中应该调用后端接口
      /*
      const response = await uni.request({
        url: 'https://your-api-domain.com/api/auth/wx-phone-login',
        method: 'POST',
        data: {
          code: wxLoginCode,
          phoneCode: code,
          encryptedData,
          iv
        }
      })
      */

      uni.hideLoading()

      // 模拟登录成功
      uni.showToast({
        title: '登录成功',
        icon: 'success',
      })

      // 跳转到首页
      setTimeout(() => {
        redirectAfterLogin()
      }, 1000)
    } catch (error: any) {
      uni.hideLoading()
      console.error('手机号登录失败:', error)
      uni.showToast({
        title: error.message || '登录失败',
        icon: 'none',
      })
    }
  } else {
    // 用户拒绝授权
    console.log('用户拒绝授权手机号')
    uni.showToast({
      title: '需要授权手机号才能登录',
      icon: 'none',
    })
  }
}

// 切换到短信登录
const switchToSmsLogin = () => {
  loginType.value = 'sms'
}

// 返回按钮
const goBack = () => {
  const pages = getCurrentPages()
  if (pages.length > 1) {
    uni.navigateBack()
  } else {
    // 如果是第一页，跳转到首页
    uni.reLaunch({
      url: '/pages/user/index',
    })
  }
}

// 页面初始化
onMounted(() => {
  // 检查微信环境
  if (PLATFORM.isMpWeixin && WxUtil.isSupportAvatarNickname()) {
    console.log('当前微信环境支持头像昵称填写能力')
  } else {
    console.log('当前微信环境不支持头像昵称填写能力，将使用默认头像')
  }
})
</script>

<style lang="scss" scoped>
.login-page {
  width: 100%;
  min-height: 100vh;
  background: #ffffff;
  position: relative;
}

/* 内容区域 - 调整顶部内边距，因为现在有固定的导航栏 */
.content-area {
  padding: 24px 24px 40px;
  width: 100%;
  box-sizing: border-box;
}

/* 表单内容 */
.form-content {
  display: flex;
  flex-direction: column;
  padding: 24px 0;
}

/* 登录按钮 */
:deep(.login-btn) {
  margin-top: 20px; // 减小顶部间距
  height: 52px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  width: 100%; // 确保按钮宽度100%
  box-shadow: none !important; // 移除阴影

  // 移除所有状态下的阴影
  &:hover,
  &:active,
  &:focus {
    box-shadow: none !important;
  }

  &.wd-button--disabled {
    background: #ccc;
    box-shadow: none !important;
  }

  // 确保文字居中
  &.wd-button {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* 表单底部 */
.form-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.link-text {
  font-size: 14px;
  color: #007aff;
  text-decoration: none;

  &:active {
    opacity: 0.7;
  }
}
</style>
