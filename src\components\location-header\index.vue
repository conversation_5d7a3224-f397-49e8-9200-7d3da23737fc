<template>
  <view class="location-info" @click="handleClick">
    <wd-icon name="location" size="16px" color="#4c4c4c" />
    <text class="header-location-text">{{ locationText }}</text>
    <wd-icon name="arrow-right" size="14px" color="#4c4c4c" />
  </view>
</template>

<script lang="ts" setup>
// 定义props
interface Props {
  locationText?: string
}

const props = withDefaults(defineProps<Props>(), {
  locationText: '北京市朝阳区 · 三里屯商圈'
})

// 定义事件
const emit = defineEmits<{
  click: []
}>()

// 处理点击事件
const handleClick = () => {
  emit('click')
}
</script>

<style lang="scss" scoped>
.location-info {
  display: flex;
  align-items: center;
  gap: 2px;
  flex: 1;

  &:active {
    opacity: 0.7;
  }
}

.header-location-text {
  font-size: 15px;
  font-weight: 400;
  color: #2e2e2e;
  line-height: 1;
}
</style>
