<!-- 商户权限模板展示组件 -->
<template>
  <view class="merchant-permission-display">
    <!-- 操作栏 -->
    <view class="permission-header">
      <view class="permission-actions">
        <wd-button size="small" @click="expandAll">全部展开</wd-button>
        <wd-button size="small" @click="collapseAll">全部收起</wd-button>
        <wd-button
          type="error"
          size="small"
          :disabled="!hasSelectedItems"
          @click="handleBatchDelete"
        >
          删除选中
        </wd-button>
      </view>
    </view>

    <!-- 权限树 -->
    <view class="permission-tree" v-if="permissionTree.length > 0">
      <!-- 一级节点 -->
      <view v-for="node in permissionTree" :key="node.id" class="permission-node-container level-1">
        <!-- 一级节点内容 -->
        <view class="permission-node" @click="toggleExpand(node.id)">
          <view class="permission-node-info">
            <view class="checkbox-container" @click.stop.prevent>
              <wd-checkbox
                :model-value="checkedPermissionIds.includes(node.id)"
                :indeterminate="getIndeterminateState(node)"
                @change="(checked) => handlePermissionCheck(node.id, checked)"
                @click.stop.prevent
                custom-class="permission-checkbox"
              />
            </view>
            <!-- 展开/收起图标 -->
            <view
              class="expand-icon"
              v-if="node.children && node.children.length > 0"
              @click.stop="toggleExpand(node.id)"
            >
              <wd-icon
                :name="expandedPermissions.includes(node.id) ? 'arrow-down' : 'arrow-right'"
                size="14"
                custom-class="expand-toggle-icon"
                :class="{ rotated: expandedPermissions.includes(node.id) }"
              />
            </view>
            <!-- 节点图标 -->
            <wd-icon
              v-if="node.icon"
              :name="node.icon"
              size="16"
              custom-class="permission-node-icon"
            />
            <!-- 节点信息 -->
            <text class="permission-node-title">{{ node.label }}</text>
            <text
              class="permission-node-badge"
              :class="getPermissionTypeClass(node.permission_type)"
            >
              {{ getPermissionTypeText(node.permission_type) }}
            </text>
          </view>

          <!-- 操作按钮 -->
          <view class="permission-node-actions" @click.stop.prevent>
            <wd-button type="warning" size="small" @click.stop="editPermission(node)">
              编辑
            </wd-button>
            <wd-button type="success" size="small" @click.stop="addChildPermission(node)">
              新增
            </wd-button>
          </view>
        </view>

        <!-- 子节点容器 -->
        <view
          v-if="node.children && node.children.length > 0"
          class="permission-children level-2"
          :class="{ expanded: expandedPermissions.includes(node.id) }"
        >
          <!-- 二级节点 -->
          <view
            v-for="child in node.children"
            :key="child.id"
            class="permission-node-container level-2"
          >
            <!-- 二级节点内容 -->
            <view class="permission-node" @click="toggleExpand(child.id)">
              <view class="permission-node-info">
                <view class="checkbox-container" @click.stop.prevent>
                  <wd-checkbox
                    :model-value="checkedPermissionIds.includes(child.id)"
                    :indeterminate="getIndeterminateState(child)"
                    @change="(checked) => handlePermissionCheck(child.id, checked)"
                    @click.stop.prevent
                    custom-class="permission-checkbox"
                  />
                </view>
                <!-- 展开/收起图标 -->
                <view
                  class="expand-icon"
                  v-if="child.children && child.children.length > 0"
                  @click.stop="toggleExpand(child.id)"
                >
                  <wd-icon
                    :name="expandedPermissions.includes(child.id) ? 'arrow-down' : 'arrow-right'"
                    size="12"
                    custom-class="expand-toggle-icon"
                    :class="{ rotated: expandedPermissions.includes(child.id) }"
                  />
                </view>
                <!-- 节点图标 -->
                <wd-icon
                  v-if="child.icon"
                  :name="child.icon"
                  size="14"
                  custom-class="permission-node-icon"
                />
                <!-- 节点信息 -->
                <text class="permission-node-title">{{ child.label }}</text>
                <text
                  class="permission-node-badge"
                  :class="getPermissionTypeClass(child.permission_type)"
                >
                  {{ getPermissionTypeText(child.permission_type) }}
                </text>
              </view>

              <!-- 操作按钮 -->
              <view class="permission-node-actions" @click.stop.prevent>
                <wd-button type="warning" size="small" @click.stop="editPermission(child)">
                  编辑
                </wd-button>
                <wd-button type="success" size="small" @click.stop="addChildPermission(child)">
                  新增
                </wd-button>
              </view>
            </view>

            <!-- 三级节点容器 -->
            <view
              v-if="child.children && child.children.length > 0"
              class="permission-children level-3"
              :class="{ expanded: expandedPermissions.includes(child.id) }"
            >
              <!-- 三级节点 -->
              <view
                v-for="grandChild in child.children"
                :key="grandChild.id"
                class="permission-node-container level-3"
              >
                <!-- 三级节点内容 -->
                <view class="permission-node">
                  <view class="permission-node-info">
                    <view class="checkbox-container" @click.stop.prevent>
                      <wd-checkbox
                        :model-value="checkedPermissionIds.includes(grandChild.id)"
                        @change="(checked) => handlePermissionCheck(grandChild.id, checked)"
                        @click.stop.prevent
                        custom-class="permission-checkbox"
                      />
                    </view>
                    <!-- 节点图标 -->
                    <wd-icon
                      v-if="grandChild.icon"
                      :name="grandChild.icon"
                      size="12"
                      custom-class="permission-node-icon"
                    />
                    <!-- 节点信息 -->
                    <text class="permission-node-title">{{ grandChild.label }}</text>
                    <text
                      class="permission-node-badge"
                      :class="getPermissionTypeClass(grandChild.permission_type)"
                    >
                      {{ getPermissionTypeText(grandChild.permission_type) }}
                    </text>
                  </view>

                  <!-- 操作按钮 -->
                  <view class="permission-node-actions" @click.stop.prevent>
                    <wd-button type="warning" size="small" @click.stop="editPermission(grandChild)">
                      编辑
                    </wd-button>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else class="permission-empty">
      <wd-status-tip type="search" tip="暂无权限数据" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// 权限节点类型定义
export interface IPermissionTemplateNode {
  id: string
  label: string
  permission_code: string
  parent_id?: string | null
  permission_type: number
  visible: number
  icon?: string
  order_num: number
  path?: string
  component?: string
  description?: string
  children: IPermissionTemplateNode[]
}

// 组件 Props
interface Props {
  permissionTree: IPermissionTemplateNode[]
  modelValue: string[]
}

// 组件 Emits
interface Emits {
  (e: 'update:modelValue', value: string[]): void

  (e: 'edit-permission', node: IPermissionTemplateNode): void

  (e: 'add-child-permission', node: IPermissionTemplateNode): void

  (e: 'delete-permission', node: IPermissionTemplateNode): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 内部状态
const checkedPermissionIds = ref<string[]>([])
const expandedPermissions = ref<string[]>([])

// 监听外部传入的选中状态
watch(
  () => props.modelValue,
  (newVal) => {
    if (JSON.stringify(checkedPermissionIds.value) !== JSON.stringify(newVal)) {
      checkedPermissionIds.value = [...newVal]
    }
  },
  { immediate: true },
)

// 监听内部选中状态变化，向外发射事件
watch(
  () => checkedPermissionIds.value,
  (newVal) => {
    if (JSON.stringify(props.modelValue) !== JSON.stringify(newVal)) {
      emit('update:modelValue', [...newVal])
    }
  },
  { deep: true },
)

// 权限类型相关函数
const getPermissionTypeText = (type: number) => {
  switch (type) {
    case 1:
      return '目录'
    case 2:
      return '菜单'
    case 3:
      return '按钮'
    default:
      return '未知'
  }
}

const getPermissionTypeClass = (type: number) => {
  switch (type) {
    case 1:
      return 'type-directory'
    case 2:
      return 'type-menu'
    case 3:
      return 'type-button'
    default:
      return ''
  }
}

// 获取节点的半选状态
const getIndeterminateState = (node: IPermissionTemplateNode) => {
  if (!node.children || node.children.length === 0) return false
  if (checkedPermissionIds.value.includes(node.id)) return false

  const checkedChildren = node.children.filter((child) =>
    checkedPermissionIds.value.includes(child.id),
  )

  return checkedChildren.length > 0 && checkedChildren.length < node.children.length
}

// 切换节点展开状态
const toggleExpand = (nodeId: string) => {
  const index = expandedPermissions.value.indexOf(nodeId)
  if (index > -1) {
    expandedPermissions.value.splice(index, 1)
  } else {
    expandedPermissions.value.push(nodeId)
  }
}

// 展开所有节点
const expandAll = () => {
  const allIds: string[] = []
  const collectIds = (nodes: IPermissionTemplateNode[]) => {
    nodes.forEach((node) => {
      allIds.push(node.id)
      if (node.children && node.children.length > 0) {
        collectIds(node.children)
      }
    })
  }
  collectIds(props.permissionTree)
  expandedPermissions.value = allIds
}

// 收起所有节点
const collapseAll = () => {
  expandedPermissions.value = []
}

// 全选
const checkAll = () => {
  const allIds: string[] = []
  const collectIds = (nodes: IPermissionTemplateNode[]) => {
    nodes.forEach((node) => {
      allIds.push(node.id)
      if (node.children && node.children.length > 0) {
        collectIds(node.children)
      }
    })
  }
  collectIds(props.permissionTree)
  checkedPermissionIds.value = allIds
}

// 取消全选
const uncheckAll = () => {
  checkedPermissionIds.value = []
}

// 递归获取所有子节点ID
const getAllChildrenIds = (node: IPermissionTemplateNode): string[] => {
  const ids: string[] = []
  if (node.children && node.children.length > 0) {
    node.children.forEach((child) => {
      ids.push(child.id)
      ids.push(...getAllChildrenIds(child))
    })
  }
  return ids
}

// 查找父节点
const findParentNode = (targetId: string, nodes: IPermissionTemplateNode[]): string | null => {
  for (const node of nodes) {
    if (node.children && node.children.some((child) => child.id === targetId)) {
      return node.id
    }
    if (node.children && node.children.length > 0) {
      const found = findParentNode(targetId, node.children)
      if (found) return found
    }
  }
  return null
}

// 处理权限选择
const handlePermissionCheck = (permissionId: string, checked: boolean | { value: boolean }) => {
  const isChecked = typeof checked === 'boolean' ? checked : checked.value

  // 查找当前节点
  const findNode = (
    id: string,
    nodes: IPermissionTemplateNode[],
  ): IPermissionTemplateNode | null => {
    for (const node of nodes) {
      if (node.id === id) return node
      if (node.children && node.children.length > 0) {
        const found = findNode(id, node.children)
        if (found) return found
      }
    }
    return null
  }

  const currentNode = findNode(permissionId, props.permissionTree)
  if (!currentNode) return

  let newCheckedIds = [...checkedPermissionIds.value]

  if (isChecked) {
    // 选中当前节点
    if (!newCheckedIds.includes(permissionId)) {
      newCheckedIds.push(permissionId)
    }
  } else {
    // 取消当前节点
    const index = newCheckedIds.indexOf(permissionId)
    if (index > -1) {
      newCheckedIds.splice(index, 1)
    }
  }

  checkedPermissionIds.value = newCheckedIds
}

// 操作按钮事件
const editPermission = (node: IPermissionTemplateNode) => {
  emit('edit-permission', node)
}

const addChildPermission = (node: IPermissionTemplateNode) => {
  emit('add-child-permission', node)
}

// 判断是否有选中项
const hasSelectedItems = computed(() => checkedPermissionIds.value.length > 0)

// 批量删除选中的权限
const handleBatchDelete = () => {
  if (!checkedPermissionIds.value.length) {
    uni.showToast({
      title: '请选择要删除的权限',
      icon: 'none',
    })
    return
  }

  // 找到所有选中的节点
  const selectedNodes = checkedPermissionIds.value
    .map((id) => {
      const findNode = (nodes: IPermissionTemplateNode[]): IPermissionTemplateNode | null => {
        for (const node of nodes) {
          if (node.id === id) return node
          if (node.children && node.children.length > 0) {
            const found = findNode(node.children)
            if (found) return found
          }
        }
        return null
      }
      return findNode(props.permissionTree)
    })
    .filter(Boolean) as IPermissionTemplateNode[]

  // 确认删除
  uni.showModal({
    title: '确认删除',
    content: `确定要删除选中的 ${selectedNodes.length} 个权限吗？删除后将无法恢复，请谨慎操作。`,
    success: (res) => {
      if (res.confirm) {
        // 发出删除事件，让父组件处理删除逻辑
        selectedNodes.forEach((node) => {
          emit('delete-permission', node)
        })
        // 清空选中状态
        checkedPermissionIds.value = []
      }
    },
  })
}
</script>

<style lang="scss" scoped>
.merchant-permission-display {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
}

.permission-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eee;
}

.permission-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
  justify-content: flex-start;

  .wd-button {
    min-width: 64px;
  }
}

.permission-tree {
  padding: 8px 0;
}

.permission-empty {
  padding: 30px 20px;
  text-align: center;
}

// 权限节点样式
.permission-node-container {
  margin-bottom: 2px;

  &.level-1 {
    background-color: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    margin-bottom: 4px;
  }

  &.level-2 {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
  }

  &.level-3 {
    background-color: #fbfbfb;
    border-bottom: 1px solid #f0f0f0;
    margin: 1px 0;
  }
}

.permission-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  transition: background-color 0.2s ease;
}

.permission-node-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.permission-node-actions {
  display: flex;
  gap: 4px;
  align-items: center;
  flex-shrink: 0;
}

.expand-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  margin-right: 4px;

  &:hover {
    background-color: rgba(66, 133, 244, 0.1);
  }
}

:deep(.expand-toggle-icon) {
  color: #4285f4 !important;
  transition: transform 0.3s ease !important;
}

.permission-children {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  transition:
    max-height 0.3s ease,
    opacity 0.3s ease;
  padding-left: 20px;
  border-left: 2px solid #e0e0e0;
  margin-left: 16px;

  &.expanded {
    max-height: 2000px; // 足够大的值来容纳所有内容
    opacity: 1;
  }

  &.level-3 {
    margin-top: 4px;
    padding-left: 16px;
  }
}

.checkbox-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 20px;
}

:deep(.permission-checkbox) {
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 0 !important;
}

.permission-node-icon {
  color: #666;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.permission-node-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.permission-node-badge {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  white-space: nowrap;
  flex-shrink: 0;
  display: flex;
  align-items: center;

  &.type-directory {
    background-color: #fff3e0;
    color: #e65100;
  }

  &.type-menu {
    background-color: #e3f2fd;
    color: #1976d2;
  }

  &.type-button {
    background-color: #e8f5e8;
    color: #388e3c;
  }
}

// 操作按钮样式
.permission-node-actions :deep(.wd-button) {
  font-size: 10px !important;
  padding: 4px 8px !important;
  height: 22px !important;
  min-width: auto !important;
  line-height: 1 !important;
  margin: 0 2px !important;
}

// 展开图标旋转动画
:deep(.expand-toggle-icon) {
  color: #4285f4 !important;
  transition: transform 0.3s ease !important;
}
</style>
