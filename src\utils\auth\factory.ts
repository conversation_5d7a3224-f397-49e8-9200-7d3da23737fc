import { userTypeEnum, useGlobalRole } from '@/store/globalRole'
import { SystemAdapter } from './adapters/systemAdapter'
import { MerchantAdapter } from './adapters/merchantAdapter'
import { UserAdapter } from './adapters/userAdapter'

// 适配器基础接口
export interface AuthAdapter {
  getToken(): string | null

  getUserType(): userTypeEnum


  getDefaultHeaders(): Record<string, string>

  handleResponseHeaders(headers: Record<string, string>): Promise<boolean>

  handleUnauthorized(): Promise<boolean>

  handleForbidden(): void

  handleLogout(): void

  isLoggedIn(): boolean

  getUserInfo(): any
}

// 适配器实例缓存
const adapterCache = new Map<userTypeEnum, AuthAdapter>()

/**
 * 根据用户类型获取适配器
 */
export const getAuthAdapter = (userType?: userTypeEnum): AuthAdapter => {
  // 如果没有指定用户类型，从全局状态获取
  const targetUserType = userType || useGlobalRole().getRole

  // 检查缓存
  if (adapterCache.has(targetUserType)) {
    return adapterCache.get(targetUserType)!
  }

  // 创建对应的适配器实例
  let adapter: AuthAdapter

  switch (targetUserType) {
    case userTypeEnum.system:
      adapter = new SystemAdapter()
      break
    case userTypeEnum.merchants:
      adapter = new MerchantAdapter()
      break
    case userTypeEnum.user:
      adapter = new UserAdapter()
      break
    default:
      // 默认使用普通用户适配器
      adapter = new UserAdapter()
      console.warn('未知的用户类型，使用默认的普通用户适配器')
  }

  // 缓存适配器实例
  adapterCache.set(targetUserType, adapter)

  return adapter
}

/**
 * 清除适配器缓存
 */
export const clearAdapterCache = (userType?: userTypeEnum) => {
  if (userType) {
    adapterCache.delete(userType)
  } else {
    adapterCache.clear()
  }
}



// 导出便捷方法
export const getSystemAdapter = () =>
  getAuthAdapter(userTypeEnum.system) as unknown as SystemAdapter

export const getMerchantAdapter = () =>
  getAuthAdapter(userTypeEnum.merchants) as unknown as MerchantAdapter

export const getUserAdapter = () =>
  getAuthAdapter(userTypeEnum.user) as unknown as UserAdapter
