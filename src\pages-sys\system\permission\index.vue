<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '权限管理',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="permission-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      title="权限管理"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <view class="main-layout">
        <!-- 权限管理页面内容 -->
        <view class="permission-management">
          <!-- 操作栏 -->
          <view class="action-bar">
            <!-- 按钮栏（无搜索条件） -->
            <view class="button-row">
              <view class="left-buttons">
                <wd-button type="success" size="small" @click="addPermission">新增</wd-button>
                <wd-button
                  type="error"
                  size="small"
                  @click="batchDeletePermissions"
                  :disabled="selectedPermissions.length === 0"
                >
                  批量删除
                  {{ selectedPermissions.length > 0 ? `(${selectedPermissions.length})` : '' }}
                </wd-button>
                <wd-button
                  type="info"
                  size="small"
                  icon="refresh"
                  @click="refreshData"
                ></wd-button>
              </view>
            </view>
          </view>

          <!-- 权限列表 -->
          <view class="permission-list">
            <!-- 权限树形展示 -->
            <view class="tree-mode" v-if="!loading">
              <permission-display
                :permission-tree="permissionTreeData"
                v-model="selectedPermissions"
                :readonly="false"
                @view-permission="handleViewPermission"
                @edit-permission="handleEditPermission"
                @add-child-permission="handleAddChildPermission"
              ></permission-display>
            </view>

            <!-- 加载状态 -->
            <view v-if="loading" class="loading-state">
              <text class="loading-text">加载中...</text>
            </view>

            <!-- 空状态 -->
            <wd-status-tip
              v-if="permissionTreeData.length === 0 && !loading"
              type="search"
              tip="暂无权限数据"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
// @ts-ignore
import PermissionDisplay from './permission-display.vue'
// @ts-ignore
import {
  getPermissionTreeApi,
  deletePermissionApi,
  batchDeletePermissionsApi,
  type IPermissionTreeNode,
} from '../../../api/sys/systemPermissionApi'

defineOptions({
  name: 'PermissionManagement',
})

// 权限数据
const loading = ref(false)

// 搜索条件
const searchMenuName = ref('')
const searchMenuType = ref('')
const searchStatus = ref('')

// 权限树数据 - 使用API获取
const permissionTreeData = ref<IPermissionTreeNode[]>([])

// 选中的权限节点
const selectedPermissions = ref<string[]>([])

// 菜单类型选项
const menuTypeOptions = ref([
  { label: '全部类型', value: '' },
  { label: '目录', value: '0' },
  { label: '菜单', value: '1' },
  { label: '按钮', value: '2' },
])

// 状态选项
const statusOptions = ref([
  { label: '全部', value: '' },
  { label: '正常', value: '0' },
  { label: '停用', value: '1' },
])

// 获取菜单类型文本
const getMenuTypeText = (type: number) => {
  const typeMap = {
    0: '目录',
    1: '菜单',
    2: '按钮',
  }
  return typeMap[type] || '未知'
}

// 处理搜索
const handleSearch = () => {
  console.log('搜索权限数据')
  loadPermissionTree() // 重新加载权限树
}

// 菜单类型选择器变化事件
const onMenuTypeChange = ({ value }: { value: string }) => {
  console.log('菜单类型变化:', value)
  handleSearch() // 自动触发搜索
}

// 状态选择器变化事件
const onStatusChange = ({ value }: { value: string }) => {
  console.log('状态变化:', value)
  handleSearch() // 自动触发搜索
}

// 重置搜索
const resetSearch = () => {
  searchMenuName.value = ''
  searchMenuType.value = ''
  searchStatus.value = ''
  handleSearch() // 重新加载数据
}

// 刷新数据
const refreshData = () => {
  loadPermissionTree()
  uni.showToast({
    title: '数据已刷新',
    icon: 'success',
  })
}

// 新增权限
const addPermission = () => {
  console.log('新增权限')
  uni.navigateTo({
    url: '/pages-sys/system/permission/permission-form?mode=create',
  })
}

// 权限展示组件事件处理
const handleViewPermission = (node: IPermissionTreeNode) => {
  console.log('查看权限详情:', node)
  uni.navigateTo({
    url: `/pages-sys/system/permission/permission-form?mode=view&id=${node.id}`,
  })
}

const handleEditPermission = (node: IPermissionTreeNode) => {
  console.log('编辑权限:', node)
  uni.navigateTo({
    url: `/pages-sys/system/permission/permission-form?mode=edit&id=${node.id}`,
  })
}

const handleAddChildPermission = (node: IPermissionTreeNode) => {
  console.log('新增子权限，父节点:', node)
  uni.navigateTo({
    url: `/pages-sys/system/permission/permission-form?mode=create&parent_id=${node.id}`,
  })
}

// 删除权限
const deletePermission = (permission: any) => {
  console.log('删除权限:', permission)
  uni.showModal({
    title: '确认删除',
    content: `确定要删除权限 "${permission.menu_name}" 吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          await deletePermissionApi(permission.id)
          uni.showToast({
            title: '删除成功',
            icon: 'success',
          })
          // 重新加载数据
          loadPermissionTree()
        } catch (error) {
          console.error('删除权限失败:', error)
          uni.showToast({
            title: '删除权限失败',
            icon: 'none',
          })
        }
      }
    },
  })
}

// 返回功能
const handleBack = () => {
  console.log('返回功能')
  uni.navigateBack()
}

// 加载权限树数据
const loadPermissionTree = async () => {
  try {
    loading.value = true
    const result = await getPermissionTreeApi()
    permissionTreeData.value = result.data
    console.log('权限树数据加载成功:', result.data)
  } catch (error) {
    console.error('加载权限树数据失败:', error)
    await uni.showToast({
      title: '加载权限树失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 批量删除权限
const batchDeletePermissions = () => {
  if (selectedPermissions.value.length === 0) {
    uni.showToast({
      title: '请先选择要删除的权限',
      icon: 'none',
    })
    return
  }

  uni.showModal({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedPermissions.value.length} 项权限吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const res = await batchDeletePermissionsApi({
            permission_ids: selectedPermissions.value,
          })
          if (res.code !== 200) {
            await uni.showToast({
              title: res.message,
              icon: 'none',
            })
          } else {
            await uni.showToast({
              title: '删除成功',
              icon: 'success',
            })
          }
          selectedPermissions.value = []
          // 重新加载数据
          await loadPermissionTree()
        } catch (error) {
          console.error('批量删除权限失败:', error)
          await uni.showToast({
            title: error.message,
            icon: 'none',
          })
        }
      }
    },
  })
}

onMounted(() => {
  console.log('权限管理页面挂载完成')
  // 只加载权限树数据
  loadPermissionTree()
})

// 页面显示时的处理，从其他页面返回时会触发
onShow(() => {
  console.log('权限管理页面显示')
  // 重新加载权限树数据，确保显示最新数据
  loadPermissionTree()
  // 清空之前的选择状态
  selectedPermissions.value = []
})
</script>

<style lang="scss" scoped>
.permission-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

// 标题文字设为白色
:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

// 自定义返回按钮样式
:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  background-color: transparent;
  overflow-y: auto;
  position: relative;
}

.main-layout {
  background-color: white;
  padding: 20px;
  min-height: 100%;
}

.permission-management {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-bar {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 0 0 10px 0;
  border-bottom: 1px solid #eee;
}

.top-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-bar {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.search-row {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.search-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1px;
}

.search-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
  min-width: 45px;
}

.search-input-component {
  width: 120px;
  height: 28px;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  background-color: white !important;
  overflow: hidden;
}

:deep(.search-input-component .wd-input) {
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

:deep(.search-input-component .wd-input__inner) {
  padding: 6px 10px;
  font-size: 12px;
  height: 28px;
  box-sizing: border-box;
  border: none;
  background-color: transparent;
}

:deep(.search-input-component .wd-input:focus-within) {
  border-color: #4285f4;
}

:deep(.search-input-component .wd-input__inner:focus) {
  outline: none;
}


.picker-text {
  font-size: 12px;
}

.button-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.right-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.permission-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.loading-state {
  text-align: center;
  padding: 60px 20px;
}

.loading-text {
  color: #999;
  font-size: 14px;
}

/* 时间选择器对齐样式 */
:deep(.datetime-picker-custom) {
  display: flex;
  align-items: center;
  height: 28px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

:deep(.datetime-picker-custom .wd-datetime-picker__inner) {
  height: 28px;
  line-height: 28px;
  padding: 0 10px;
  font-size: 12px;
  border: none;
  background: transparent;
}
</style>
