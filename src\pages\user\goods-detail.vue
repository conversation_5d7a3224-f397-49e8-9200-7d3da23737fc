<route lang="json5" type="page">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '商品详情',
  },
  access: {
    requireAuth: false,
  },
}
</route>

<template>
  <view class="goods-detail-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: safeAreaInsetsTop + 'px' }">
      <view class="navbar-content" :style="{ paddingRight: rightSafeArea + 'px' }">
        <view class="navbar-left" @click="goBack" @tap="goBack">
          <wd-icon name="arrow-left" size="24px" color="#333" />
        </view>
        <view class="navbar-center">
          <view class="merchant-info">
            <image class="merchant-logo" :src="merchantInfo.logo" mode="aspectFill" />
            <text class="merchant-name">{{ merchantInfo.name }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 商品轮播图 -->
    <view class="goods-swiper-container">
      <wd-swiper
        :list="goodsImages"
        :autoplay="true"
        :interval="4000"
        :duration="300"
        :loop="true"
        :current="currentImageIndex"
        :indicator="indicatorConfig"
        indicator-position="bottom-right"
        @click="previewImage"
        @change="onSwiperChange"
        height="400px"
      />
    </view>

    <!-- 商品基本信息 -->
    <view class="goods-info-card">
      <view class="price-section">
        <view class="price-main">
          <view class="current-price-row">
            <text class="price-symbol">¥</text>
            <text class="current-price">{{ goodsInfo.price }}</text>
          </view>
        </view>
        <view class="collect-btn" @click="toggleCollect">
          <wd-icon
            :name="isCollected ? 'star-on' : 'star'"
            :color="isCollected ? '#ffca28' : '#999'"
            size="28px"
          />
          <text class="collect-text" :class="{ collected: isCollected }">收藏</text>
        </view>
      </view>

      <view class="title-section">
        <text class="goods-title">{{ goodsInfo.title }}</text>
      </view>

      <view class="sales-info-section">
        <view class="info-item">
          <wd-icon name="location" size="14px" color="#999" />
          <text class="info-text">深圳市</text>
        </view>
        <view class="info-item">
          <text class="info-label">销量</text>
          <text class="info-value">{{ goodsInfo.sales }}件</text>
        </view>
        <view class="info-item">
          <text class="info-label">库存</text>
          <text class="info-value">{{ goodsInfo.stock }}件</text>
        </view>
        <view class="info-item">
          <text class="info-label">月销</text>
          <text class="info-value">{{ goodsInfo.monthlySales }}件</text>
        </view>
      </view>
    </view>

    <!-- 商铺信息 -->
    <view class="shop-info-card">
      <view class="shop-header">
        <view class="shop-basic">
          <image class="shop-avatar" :src="shopInfo.avatar" mode="aspectFill" />
          <view class="shop-detail">
            <text class="shop-name">{{ shopInfo.name }}</text>
            <text class="shop-followers">{{ shopInfo.followers }}人关注</text>
          </view>
        </view>
        <view class="shop-actions">
          <view class="action-btn follow-btn" @click="toggleFollow">
            <text class="btn-text">{{ isFollowed ? '已关注' : '关注' }}</text>
          </view>
          <view class="action-btn enter-btn" @click="enterShop">
            <text class="btn-text">进店逛逛</text>
          </view>
        </view>
      </view>

      <!-- 店铺推荐 -->
      <view class="shop-recommend-section">
        <view class="recommend-header">
          <text class="recommend-title">店铺推荐</text>
          <view class="view-all" @click="viewAllRecommend">
            <text class="view-all-text">查看全部</text>
            <wd-icon name="arrow-right" size="14px" color="#999" />
          </view>
        </view>

        <scroll-view class="recommend-scroll" :scroll-x="true" :show-scrollbar="false">
          <view class="recommend-list">
            <view
              v-for="(item, index) in recommendGoods"
              :key="index"
              class="recommend-item"
              @click="goToRecommendGoods(item)"
            >
              <image class="recommend-image" :src="item.image" mode="aspectFill" />
              <view class="recommend-info">
                <text class="recommend-name">{{ item.name }}</text>
                <view class="recommend-price">
                  <text class="price-symbol">¥</text>
                  <text class="price-value">{{ item.price }}</text>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 商品详情 -->
    <view class="goods-detail-section">
      <view class="section-title">
        <text class="title-text">商品详情</text>
      </view>

      <!-- 商品描述 -->
      <view class="description-content">
        <text class="description-text">{{ goodsInfo.description }}</text>
      </view>

      <!-- 商品详情图片 -->
      <view class="detail-images">
        <image
          v-for="(image, index) in goodsDetailImages"
          :key="index"
          class="detail-image"
          :src="image"
          mode="widthFix"
          @click="previewDetailImage(index)"
        />
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-action-bar" :style="{ paddingBottom: safeAreaInsetsBottom + 'px' }">
      <view class="action-bar-content">
        <!-- 左侧图标组 -->
        <view class="icon-group">
          <view class="action-icon" @click="shareGoods">
            <wd-icon name="share" size="20px" color="#666" />
            <text class="icon-text">分享</text>
          </view>
          <view class="action-icon" @click="goToStore">
            <wd-icon name="shop" size="20px" color="#666" />
            <text class="icon-text">店铺</text>
          </view>
          <view class="action-icon" @click="goToCart">
            <wd-icon name="cart" size="20px" color="#666" />
            <text class="icon-text">购物车</text>
          </view>
        </view>

        <!-- 右侧按钮组 -->
        <view class="button-group">
          <view class="button-container">
            <view class="cart-btn" @click="addToCart">
              <text class="btn-text">加入购物车</text>
            </view>
            <view class="buy-btn" @click="buyNow">
              <text class="btn-text">立即购买</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 规格选择弹窗 -->
    <wd-popup v-model="showSpecs" position="bottom" closable>
      <view class="specs-popup">
        <!-- 商品信息头部 -->
        <view class="popup-goods-info">
          <image class="popup-goods-image" :src="goodsImages[0]" mode="aspectFill" />
          <view class="popup-goods-detail">
            <view class="popup-price">
              <text class="popup-price-symbol">¥</text>
              <text class="popup-price-value">{{ goodsInfo.price }}</text>
            </view>
            <text class="popup-stock">库存 {{ goodsInfo.stock }} 件</text>
          </view>
        </view>

        <!-- 规格选择 -->
        <view class="specs-list">
          <view class="spec-group" v-for="(group, index) in specsData" :key="index">
            <text class="spec-group-title">{{ group.name }}</text>
            <view class="spec-options">
              <text
                v-for="option in group.options"
                :key="option.value"
                class="spec-option"
                :class="{ active: selectedSpecValues[group.name] === option.value }"
                @click="selectSpec(group.name, option.value)"
              >
                {{ option.label }}
              </text>
            </view>
          </view>
        </view>

        <!-- 数量选择 -->
        <view class="quantity-section">
          <text class="quantity-title">购买数量</text>
          <view class="quantity-controls">
            <view
              class="quantity-btn"
              :class="{ disabled: selectedQuantity <= 1 }"
              @click="decreaseQuantity"
            >
              <wd-icon name="minus" size="14px" />
            </view>
            <text class="quantity-value">{{ selectedQuantity }}</text>
            <view
              class="quantity-btn"
              :class="{ disabled: selectedQuantity >= goodsInfo.stock }"
              @click="increaseQuantity"
            >
              <wd-icon name="add" size="14px" />
            </view>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, getCurrentInstance, onMounted, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useGlobalSafeArea } from '@/hooks/useSafeArea'

defineOptions({
  name: 'GoodsDetail',
})

// 使用全局安全区域 hook
const { safeAreaInsetsTop, safeAreaInsetsBottom, rightSafeArea } = useGlobalSafeArea()

// 页面参数
const goodsId = ref('1')
const goodsTitle = ref('')

// 页面加载时获取参数
onLoad((options: any) => {
  console.log('商品详情页面参数:', options)
  if (options.id) {
    goodsId.value = options.id
  }
  if (options.title) {
    goodsTitle.value = decodeURIComponent(options.title)
    // 更新商品信息中的标题
    goodsInfo.value.title = goodsTitle.value
  }
})

// 商户信息
const merchantInfo = ref({
  id: '1',
  logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop&crop=center',
  name: '阳光果园',
})

// 商铺信息
const shopInfo = ref({
  avatar:
    'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop&crop=center',
  name: '阳光果园旗舰店',
  followers: 2580,
})

// 商品轮播图
const goodsImages = ref([
  'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=800&h=600&fit=crop',
  'https://images.unsplash.com/photo-1619546813926-a78fa6372cd2?w=800&h=600&fit=crop',
  'https://images.unsplash.com/photo-1567306226416-28f0efdc88ce?w=800&h=600&fit=crop',
  'https://images.unsplash.com/photo-1570913149827-d2ac84ab3f9a?w=800&h=600&fit=crop',
])

const currentImageIndex = ref(0)

// 轮播图指示器配置
const indicatorConfig: any = ref({
  type: 'fraction',
})

// 商品基本信息
const goodsInfo = ref({
  title: '新疆阿克苏冰糖心苹果 5斤装 新鲜红富士苹果',
  price: '29.90',
  sales: 8520,
  stock: 999,
  monthlySales: 1280,
  description:
    '新疆阿克苏冰糖心苹果，生长在天山脚下，日照充足，昼夜温差大，果肉脆甜多汁，糖心明显。每一颗都经过精心挑选，个大饱满，口感香甜，营养丰富。现摘现发，新鲜直达。',
})

// 商品详情图片
const goodsDetailImages = ref([
  'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=800&h=600&fit=crop', // 苹果特写
  'https://images.unsplash.com/photo-1619546813926-a78fa6372cd2?w=800&h=600&fit=crop', // 苹果切面展示
  'https://images.unsplash.com/photo-1567306226416-28f0efdc88ce?w=800&h=600&fit=crop', // 苹果园展示
  'https://images.unsplash.com/photo-1570913149827-d2ac84ab3f9a?w=800&h=600&fit=crop', // 苹果包装展示
  'https://images.unsplash.com/photo-1576179635662-9d1983e97e1e?w=800&h=600&fit=crop', // 苹果营养展示
])

// 规格相关
const showSpecs = ref(false)
const selectedSpecValues = ref<Record<string, string>>({})
const selectedQuantity = ref(1)

const specsData = ref([
  {
    name: '重量',
    options: [
      { label: '3斤装', value: '3jin' },
      { label: '5斤装', value: '5jin' },
      { label: '10斤装', value: '10jin' },
      { label: '20斤装', value: '20jin' },
    ],
  },
  {
    name: '规格',
    options: [
      { label: '精选果', value: 'select' },
      { label: '特级果', value: 'premium' },
      { label: '礼盒装', value: 'giftbox' },
    ],
  },
])

// 收藏相关
const isCollected = ref(false)

// 关注相关
const isFollowed = ref(false)

// 推荐商品数据
const recommendGoods = ref([
  {
    id: '1',
    name: '新疆香梨 5斤装 清甜多汁',
    price: '35.80',
    image: 'https://images.unsplash.com/photo-1568702846914-96b305d2aaeb?w=200&h=200&fit=crop',
  },
  {
    id: '2',
    name: '山东烟台红富士苹果 10斤',
    price: '45.90',
    image: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=200&h=200&fit=crop',
  },
  {
    id: '3',
    name: '陕西洛川苹果 脆甜可口',
    price: '32.50',
    image: 'https://images.unsplash.com/photo-1619546813926-a78fa6372cd2?w=200&h=200&fit=crop',
  },
  {
    id: '4',
    name: '进口蛇果 新鲜甜脆',
    price: '68.00',
    image: 'https://images.unsplash.com/photo-1567306226416-28f0efdc88ce?w=200&h=200&fit=crop',
  },
])

// 方法
const goBack = () => {
  console.log('点击返回按钮')
  const pages = getCurrentPages()
  if (pages.length > 1) {
    console.log('执行返回上一页')
    uni.navigateBack({
      delta: 1,
    })
  } else {
    console.log('当前是第一页，跳转到首页')
    // 如果是第一页，跳转到首页或商品列表页
    uni.reLaunch({
      url: '/pages/user/index',
    })
  }
}

const onSwiperChange = (e: any) => {
  currentImageIndex.value = e.current
}

const previewImage = () => {
  uni.previewImage({
    current: currentImageIndex.value,
    urls: goodsImages.value,
  })
}

const previewDetailImage = (index: number) => {
  uni.previewImage({
    current: index,
    urls: goodsDetailImages.value,
  })
}

const selectSpec = (groupName: string, value: string) => {
  selectedSpecValues.value[groupName] = value
}

const addToCart = () => {
  showSpecs.value = true
}

const buyNow = () => {
  showSpecs.value = true
}

const increaseQuantity = () => {
  if (selectedQuantity.value < goodsInfo.value.stock) {
    selectedQuantity.value++
  }
}

const decreaseQuantity = () => {
  if (selectedQuantity.value > 1) {
    selectedQuantity.value--
  }
}

const goToStore = () => {
  // 跳转到商户主页
  uni.navigateTo({
    url: `/pages/user/merchant-home?id=${merchantInfo.value.id}`,
  })
}

const goToCart = () => {
  uni.showToast({
    title: '跳转至购物车页面',
    icon: 'none',
  })
}

const contactService = () => {
  uni.showToast({
    title: '跳转至客服页面',
    icon: 'none',
  })
}

const toggleCollect = () => {
  isCollected.value = !isCollected.value
  uni.showToast({
    title: isCollected.value ? '已收藏' : '已取消收藏',
    icon: 'success',
  })
}

const toggleFollow = () => {
  isFollowed.value = !isFollowed.value
  uni.showToast({
    title: isFollowed.value ? '已关注店铺' : '已取消关注',
    icon: 'success',
  })
}

const enterShop = () => {
  // 跳转到商户主页
  uni.navigateTo({
    url: `/pages/user/merchant-home?id=${merchantInfo.value.id}`,
  })
}

const viewAllRecommend = () => {
  // 跳转到商户全部宝贝页面
  uni.navigateTo({
    url: `/pages/user/merchant-goods?id=${merchantInfo.value.id}`,
  })
}

const goToRecommendGoods = (item: any) => {
  uni.showToast({
    title: `查看商品：${item.name}`,
    icon: 'none',
  })
}

const shareGoods = () => {
  uni.showToast({
    title: '分享商品',
    icon: 'none',
  })
}

// 页面加载时初始化数据
onMounted(() => {
  // 这里可以调用API获取商品详情数据
  console.log('商品ID:', goodsId.value)
})
</script>

<style lang="scss" scoped>
.goods-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
}

.navbar-content {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 16px;
  position: relative;
}

.navbar-left,
.navbar-right {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  z-index: 1001;
  pointer-events: auto;
}

.navbar-left {
  left: 16px;
}

.navbar-left :deep(.wd-icon) {
  font-size: 24px !important;
  width: 24px !important;
  height: 24px !important;
  filter: drop-shadow(0 1px 2px rgba(255, 255, 255, 0.8));
}

.navbar-right {
  right: 16px;
}

.navbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 44px;
  position: absolute;
  left: 60px;
  right: 60px;
  top: 0;
  pointer-events: none;
  z-index: 1000;
}

.merchant-info {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
  pointer-events: auto;
  max-width: 100%;
}

.merchant-logo {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  flex-shrink: 0;
}

.merchant-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* 商品轮播图 */
.goods-swiper-container {
  margin-top: 44px;
  height: 400px;
  background: #fff;
  position: relative;
  --wot-swiper-radius: 0px;
}

.goods-swiper-container :deep(.wd-swiper) {
  height: 400px;
  width: 100%;
}

/* 商品信息卡片 */
.goods-info-card {
  background: #fff;
  padding: 14px 20px;
  margin-bottom: 10px;
  border-radius: 0;
}

.price-section {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  position: relative;
}

.price-main {
  flex: 1;
}

.current-price-row {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 18px;
  color: #ff4444;
  font-weight: 600;
}

.current-price {
  font-size: 32px;
  color: #ff4444;
  font-weight: 700;
  margin-left: 4px;
}

.collect-btn {
  position: absolute;
  right: 0;
  top: 0;
  transition: transform 0.2s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.collect-btn :deep(.wd-icon) {
  font-size: 28px !important;
  width: 28px !important;
  height: 28px !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.collect-text {
  font-size: 12px;
  color: #999;
  line-height: 1;
}

.collect-text.collected {
  color: #ffca28;
}

.title-section {
  margin-bottom: 16px;
}

.goods-title {
  font-size: 18px;
  color: #333;
  line-height: 1.4;
  font-weight: 500;
}

.sales-info-section {
  display: flex;
  gap: 24px;
  margin-bottom: 12px;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-text {
  font-size: 14px;
  color: #999;
}

.info-label {
  font-size: 12px;
  color: #999;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

/* 商品详情 */
.goods-detail-section {
  background: #fff;
  margin-bottom: 80px;

}

.section-title {
  margin-bottom: 10px;
  padding-bottom: 8px;
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.title-text {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.description-content {
  margin-bottom: 10px;
  padding: 10px;
}

.description-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.detail-images {
  display: flex;
  flex-direction: column;
}

.detail-image {
  width: 100%;
}

/* 底部操作栏 */
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1px solid #eee;
  padding: 6px 12px 12px 16px;
  z-index: 1000;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.action-bar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50px;
  max-width: 100%;
}

.icon-group {
  display: flex;
  gap: 16px;
  flex: 0 0 auto;
  margin-right: 16px;
}

.action-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4px;
  transition: opacity 0.2s;
}

.icon-text {
  font-size: 10px;
  color: #666;
  margin-top: 2px;
  line-height: 1;
}

.button-group {
  display: flex;
  flex: 1;
}

.button-container {
  display: flex;
  align-items: center;
  height: 44px;
  border-radius: 22px;
  background: transparent;
  overflow: hidden;
  width: 100%;
  position: relative;
}

.cart-btn,
.buy-btn {
  flex: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.2s;
}

.cart-btn {
  background: #ff9500;
  border-radius: 22px 0 0 22px;
}

.buy-btn {
  background: #ff6b35;
  border-radius: 0 22px 22px 0;
}

.btn-text {
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  line-height: 1;
}

/* 商铺信息卡片样式 */
.shop-info-card {
  background: #fff;
  padding: 20px;
  margin-bottom: 10px;
  border-radius: 0;
}

.shop-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.shop-basic {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.shop-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.shop-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.shop-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.shop-followers {
  font-size: 12px;
  color: #999;
}

.shop-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.action-btn {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 16px;
  transition: opacity 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  min-width: 50px;
}

.follow-btn {
  background: transparent;
}

.follow-btn .btn-text {
  font-size: 12px;
  color: #666;
  font-weight: 400;
  line-height: 1;
}

.enter-btn {
  background: #ff7300;
  border-color: #ff7300;
}

.enter-btn .btn-text {
  font-size: 12px;
  color: #fff;
  font-weight: 400;
  line-height: 1;
}

/* 店铺推荐样式 */
.shop-recommend-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.recommend-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.recommend-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.view-all {
  display: flex;
  align-items: center;
  gap: 4px;
}

.view-all-text {
  font-size: 12px;
  color: #999;
}

.recommend-scroll {
  overflow-x: auto;
  white-space: nowrap;
}

.recommend-scroll::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}

.recommend-list {
  display: flex;
  gap: 16px;
  padding-bottom: 4px;
}

.recommend-item {
  flex-shrink: 0;
  width: 100px;
}

.recommend-image {
  width: 100px;
  height: 100px;
  border-radius: 6px;
}

.recommend-info {
  margin-top: 8px;
  width: 100px;
}

.recommend-name {
  font-size: 12px;
  color: #333;
  line-height: 1.2;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 28px;
  margin-bottom: 4px;
}

.recommend-price {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 10px;
  color: #ff4444;
}

.price-value {
  font-size: 14px;
  color: #ff4444;
  font-weight: 600;
}

/* 规格弹窗样式 */
.specs-popup {
  padding: 0;
  height: 60vh;
  overflow-y: auto;
}

.popup-goods-info {
  display: flex;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.popup-goods-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  margin-right: 12px;
}

.popup-goods-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.popup-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 4px;
}

.popup-price-symbol {
  font-size: 14px;
  color: #ff4444;
  font-weight: 600;
}

.popup-price-value {
  font-size: 20px;
  color: #ff4444;
  font-weight: 700;
  margin-left: 2px;
}

.popup-stock {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.specs-list {
  padding: 20px;
  background: #fff;
}

.spec-group {
  margin-bottom: 20px;
}

.spec-group:last-child {
  margin-bottom: 0;
}

.spec-group-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 12px;
  display: block;
  font-weight: 500;
}

.spec-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.spec-option {
  padding: 8px 16px;
  background: #fff;
  border-radius: 20px;
  font-size: 14px;
  color: #333;
  border: 1px solid #e0e0e0;
  transition: all 0.3s;
}

.spec-option.active {
  background: #fff;
  color: #ff6b35;
  border-color: #ff6b35;
  border-width: 1px;
}

.quantity-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
}

.quantity-title {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.quantity-controls {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20px;
  padding: 4px;
  gap: 0;
}

.quantity-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 50%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.quantity-btn.disabled {
  opacity: 0.5;
  background: #f0f0f0;
  box-shadow: none;
}

.quantity-value {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  min-width: 40px;
  text-align: center;
  padding: 0 8px;
}
</style>
