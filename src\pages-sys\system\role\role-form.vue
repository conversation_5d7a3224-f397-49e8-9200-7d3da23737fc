<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '角色表单',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="role-form-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      :title="isEditMode ? '编辑角色' : isViewMode ? '查看角色' : '新增角色'"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <view class="main-layout">
        <view class="role-form">
          <wd-form ref="formRef" :model="roleForm" :rules="formRules" errorType="message">
            <!-- 基本信息 -->
            <view class="form-section">
              <view class="section-title">基本信息</view>

              <wd-cell-group border>
                <!-- 角色名称 -->
                <wd-input
                  label="角色名称"
                  label-width="80px"
                  prop="name"
                  v-model="roleForm.name"
                  placeholder="请输入角色名称"
                  :maxlength="30"
                  show-word-limit
                  clearable
                  :rules="formRules.name"
                  :readonly="isReadOnly"
                />

                <!-- 角色描述 -->
                <wd-input
                  label="角色描述"
                  label-width="80px"
                  prop="description"
                  v-model="roleForm.description"
                  placeholder="请输入角色描述"
                  :maxlength="100"
                  show-word-limit
                  clearable
                  :readonly="isReadOnly"
                />

                <!-- 备注 -->
                <wd-textarea
                  label="备注"
                  label-width="80px"
                  prop="remark"
                  v-model="roleForm.remark"
                  placeholder="请输入备注信息"
                  :maxlength="200"
                  show-word-limit
                  :rows="1"
                  :readonly="isReadOnly"
                  auto-height
                />
              </wd-cell-group>
            </view>

            <!-- 权限设置 -->
            <view class="form-section">
              <view class="section-title">权限设置</view>

              <permission-tree
                :permission-tree="permissionTree"
                v-model="checkedPermissionIds"
                :readonly="isReadOnly"
              />
            </view>
          </wd-form>

          <!-- 操作按钮 -->
          <view class="form-actions" v-if="!isViewMode">
            <wd-button @click="handleCancel" size="large" custom-class="cancel-btn">取消</wd-button>
            <wd-button
              type="primary"
              @click="handleSubmit"
              size="large"
              :loading="submitting"
              custom-class="submit-btn"
              :disabled="isReadOnly"
            >
              {{ isEditMode ? '更新' : isViewMode ? '查看' : '创建' }}
            </wd-button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {
  createSystemRoleApi,
  updateSystemRoleApi,
  getSystemRoleDetailApi,
  assignRolePermissionsApi,
  type ISysRoleCreateRequest,
  type ISysRoleUpdateRequest,
  type ISysRoleDetailResponse,
  type ISysRolePermissionRequest,
} from '@/api/sys/systemRoleApi'
import { getPermissionTreeApi, type IPermissionTreeNode } from '@/api/sys/systemPermissionApi'
import PermissionTree from '@/pages-sys/components/permission-tree/permission-tree.vue'

defineOptions({
  name: 'RoleForm',
})

// 页面参数
const pageParams = ref<{
  mode: 'add' | 'edit' | 'view'
  roleId?: string
}>({
  mode: 'add',
})

// 计算属性
const isEditMode = computed(() => pageParams.value.mode === 'edit')
const isViewMode = computed(() => pageParams.value.mode === 'view')
const isReadOnly = computed(() => pageParams.value.mode === 'view')

// 表单引用
const formRef = ref()

// 提交状态
const submitting = ref(false)

// 角色表单数据
const roleForm = reactive<ISysRoleCreateRequest & { id?: string }>({
  name: '',
  description: '',
  permission_ids: [],
  remark: '',
})

// 权限树数据
const permissionTree = ref<IPermissionTreeNode[]>([])
const checkedPermissionIds = ref<string[]>([])

// 监听权限选择变化，同步到表单数据
watch(() => checkedPermissionIds.value, (newVal) => {
  // 避免重复设置相同的值，防止循环更新
  const newPermissionIds = [...newVal]
  if (JSON.stringify(roleForm.permission_ids) !== JSON.stringify(newPermissionIds)) {
    roleForm.permission_ids = newPermissionIds
  }
}, { deep: true })

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入角色名称' },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_\-\s]{2,30}$/,
      message: '角色名称只能包含中英文、数字、下划线、短横线，长度2-30位',
    },
  ],
} as any

// 加载权限树数据
const loadPermissionTree = async () => {
  try {
    uni.showLoading({ title: '加载权限数据...' })

    const result = await getPermissionTreeApi()
    permissionTree.value = result.data

    console.log('权限树数据加载成功:', permissionTree.value)
  } catch (error) {
    console.error('加载权限树数据失败:', error)
    uni.showToast({
      title: '加载权限数据失败',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 将平铺的权限数据转换为树形结构（用于查看模式）
const convertPermissionsToTree = (permissions: any[]) => {
  const permissionMap = new Map()
  const rootNodes: IPermissionTreeNode[] = []

  // 先将所有权限转换为节点并建立映射
  permissions.forEach(permission => {
    const node: IPermissionTreeNode = {
      id: permission.id,
      menu_name: permission.menu_name,
      perms: permission.perms,
      menu_type: permission.menu_type,
      parent_id: permission.parent_id,
      icon: '', // 查看模式下图标信息可能不完整，设为空
      children: []
    }
    permissionMap.set(permission.id, node)
  })

  // 构建树形结构
  permissions.forEach(permission => {
    const node = permissionMap.get(permission.id)
    if (permission.parent_id && permissionMap.has(permission.parent_id)) {
      // 有父节点，添加到父节点的children中
      const parentNode = permissionMap.get(permission.parent_id)
      if (!parentNode.children) {
        parentNode.children = []
      }
      parentNode.children.push(node)
    } else {
      // 没有父节点，是根节点
      rootNodes.push(node)
    }
  })

  return rootNodes
}

// 加载角色详情
const loadRoleDetail = async (roleId: string) => {
  try {
    uni.showLoading({ title: '加载角色详情...' })

    const result = await getSystemRoleDetailApi(roleId)
    const roleDetail = result.data

    // 填充表单数据
    Object.assign(roleForm, {
      id: roleDetail.id,
      name: roleDetail.name,
      description: roleDetail.description || '',
      remark: roleDetail.remark || '',
    })

    // 设置权限选择
    const permissionIds = roleDetail.permissions?.map((permission) => permission.id) || []
    checkedPermissionIds.value = permissionIds

    // 如果是查看模式，使用返回的权限数据构建权限树
    if (isViewMode.value && roleDetail.permissions && roleDetail.permissions.length > 0) {
      permissionTree.value = convertPermissionsToTree(roleDetail.permissions)
      console.log('查看模式：使用角色权限数据构建权限树:', permissionTree.value)
    }

    console.log('角色详情加载成功:', roleDetail)
    console.log('角色权限IDs:', permissionIds)
  } catch (error) {
    console.error('加载角色详情失败:', error)
    uni.showToast({
      title: '加载角色详情失败',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 提交表单
const handleSubmit = async () => {
  // 如果是查看模式，直接返回
  if (isViewMode.value) {
    uni.navigateBack()
    return
  }

  try {
    // 验证表单
    const { valid, errors } = await formRef.value.validate()

    console.log('表单验证结果:', { valid, errors })

    if (!valid) {
      console.log('表单验证失败:', errors)
      uni.showToast({
        title: '请检查表单信息',
        icon: 'none',
      })
      return
    }

    console.log('表单验证通过，开始提交数据')
    submitting.value = true

    // 确保permission_ids为数组格式
    const permission_ids = Array.isArray(roleForm.permission_ids) ? roleForm.permission_ids : []
    console.log('提交前权限IDs:', permission_ids)

    if (isEditMode.value) {
      // 编辑模式 - 分步骤处理

      // 1. 更新基本信息
      const updateData: ISysRoleUpdateRequest = {
        name: roleForm.name,
        description: roleForm.description,
        remark: roleForm.remark,
      }

      console.log('更新角色基本信息:', updateData)
      await updateSystemRoleApi(pageParams.value.roleId!, updateData)

      // 2. 更新权限分配
      const permissionRequest: ISysRolePermissionRequest = {
        permission_ids: permission_ids,
      }

      console.log('更新角色权限:', permissionRequest)
      await assignRolePermissionsApi(pageParams.value.roleId!, permissionRequest)

      uni.showToast({
        title: '角色更新成功',
        icon: 'success',
      })
    } else {
      // 新增模式
      const createData: ISysRoleCreateRequest = {
        name: roleForm.name,
        description: roleForm.description,
        permission_ids: permission_ids,
        remark: roleForm.remark,
      }

      console.log('创建角色数据:', createData)
      await createSystemRoleApi(createData)

      uni.showToast({
        title: '角色创建成功',
        icon: 'success',
      })
    }

    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1000)
  } catch (error) {
    console.error('操作失败:', error)

    if (error && typeof error === 'object' && 'errors' in error) {
      console.log('表单验证异常:', error)
      uni.showToast({
        title: '表单验证失败',
        icon: 'none',
      })
    } else {
      uni.showToast({
        title: isEditMode.value ? '角色更新失败' : '角色创建失败',
        icon: 'none',
      })
    }
  } finally {
    submitting.value = false
  }
}

// 取消操作
const handleCancel = () => {
  // 如果是查看模式，直接返回
  if (isViewMode.value) {
    uni.navigateBack()
    return
  }
  
  // 编辑/新增模式才显示确认提示
  uni.showModal({
    title: '确认取消',
    content: '确定要取消操作吗？未保存的内容将丢失。',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack()
      }
    },
  })
}

// 返回功能
const handleBack = () => {
  // 如果是查看模式，直接返回
  if (isViewMode.value) {
    uni.navigateBack()
    return
  }
  
  // 编辑/新增模式调用取消逻辑
  handleCancel()
}

// 页面加载事件
onLoad((options) => {
  console.log('角色表单页面参数:', options)

  if (options) {
    pageParams.value.mode = (options.mode as 'add' | 'edit' | 'view') || 'add'
    pageParams.value.roleId = options.roleId
  }

  // 如果是编辑或查看模式，加载角色详情
  if ((isEditMode.value || isViewMode.value) && pageParams.value.roleId) {
    loadRoleDetail(pageParams.value.roleId)
  }
})

onMounted(() => {
  console.log('角色表单页面挂载完成')
  console.log('页面模式:', pageParams.value.mode)
  console.log('角色ID:', pageParams.value.roleId)

  // 只有在非查看模式下才加载权限树数据
  if (!isViewMode.value) {
    loadPermissionTree()
  }

  // 确保permission_ids始终是数组类型
  if (!Array.isArray(roleForm.permission_ids)) {
    console.warn('permission_ids不是数组，强制转换为数组:', roleForm.permission_ids)
    roleForm.permission_ids = []
  }
  console.log('初始化后的permission_ids:', roleForm.permission_ids)
})
</script>

<style lang="scss" scoped>
.role-form-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding-bottom: 80px; /* 为固定按钮留出空间 */
}

.main-layout {
  padding: 8px;
}

.form-section {
  margin-bottom: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 15px 8px 15px;
  padding-bottom: 6px;
  border-bottom: 1px solid #eee;
}

.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 12px;
  padding: 12px;
  background-color: white;
  border-top: 1px solid #eee;
  z-index: 100;
  safe-area-inset-bottom: env(safe-area-inset-bottom);
}

:deep(.cancel-btn) {
  flex: 1;
  background-color: #f5f5f5 !important;
  color: #666 !important;
  border-color: #ddd !important;
}

:deep(.submit-btn) {
  flex: 1;
}

// Wot UI 表单项样式优化
:deep(.wd-cell-group) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.wd-cell) {
  background-color: white;
}

:deep(.wd-input) {
  background-color: transparent;
}

:deep(.wd-textarea) {
  background-color: transparent;
}
</style> 