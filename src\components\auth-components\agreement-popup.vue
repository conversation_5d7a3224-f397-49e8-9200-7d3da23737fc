<template>
  <wd-popup
    :modelValue="modelValue"
    @update:modelValue="$emit('update:modelValue', $event)"
    position="center"
    :close-on-click-modal="true"
    custom-style="width: 90%; max-width: 600px;"
  >
    <view class="agreement-content">
      <view class="agreement-header">
        <text class="agreement-title">{{ title }}</text>
        <wd-icon name="close" size="20px" @click="$emit('update:modelValue', false)" />
      </view>
      <scroll-view class="agreement-scroll" scroll-y>
        <text class="agreement-detail">{{ content }}</text>
      </scroll-view>
      <view class="agreement-actions">
        <wd-button size="small" @click="$emit('update:modelValue', false)">关闭</wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
interface Props {
  modelValue: boolean
  title: string
  content: string
}

defineProps<Props>()
defineEmits(['update:modelValue'])
</script>

<style lang="scss" scoped>
.agreement-content {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.agreement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.agreement-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.agreement-scroll {
  flex: 1;
  padding: 20px;
  max-height: 60vh;
}

.agreement-detail {
  font-size: 14px;
  line-height: 1.8;
  color: #333;
  white-space: pre-line;
}

.agreement-actions {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: center;
}
</style> 