<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '商户用户管理',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="user-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      title="商户用户管理"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <view class="main-layout">
        <!-- 商户用户管理页面内容 -->
        <view class="user-management">
          <!-- 操作栏 -->
          <view class="action-bar">
            <!-- 搜索条件栏 -->
            <view class="search-bar">
              <view class="search-form">
                <!-- 第一行搜索条件 -->
                <view class="search-row">
                  <view class="search-item">
                    <text class="search-label">关键词：</text>
                    <wd-input
                      class="search-input-component"
                      placeholder="搜索用户名、手机号、邮箱"
                      v-model="searchParams.keyword"
                      clearable
                      @clear="handleSearch"
                    />
                  </view>
                  <view class="search-item">
                    <text class="search-label">真实姓名：</text>
                    <wd-input
                      class="search-input-component"
                      placeholder="请输入真实姓名"
                      v-model="searchParams.real_name"
                      clearable
                      @clear="handleSearch"
                    />
                  </view>
                </view>

                <!-- 第二行搜索条件 -->
                <view class="search-row">
                  <view class="search-item">
                    <text class="search-label">状态：</text>
                    <wd-select-picker
                      class="search-select-picker"
                      v-model="searchStatusValue"
                      :columns="statusOptions"
                      type="radio"
                      :show-confirm="false"
                      @change="onStatusChange"
                    />
                  </view>
                  <view class="search-item">
                    <text class="search-label">性别：</text>
                    <wd-select-picker
                      class="search-select-picker"
                      v-model="searchGenderValue"
                      :columns="genderOptions"
                      type="radio"
                      :show-confirm="false"
                      @change="onGenderChange"
                    />
                  </view>
                </view>

                <!-- 按钮行 -->
                <view class="button-row">
                  <view class="left-buttons">
                    <wd-button type="success" size="small" @click="addUser">新增</wd-button>
                    <wd-button
                      type="info"
                      size="small"
                      icon="refresh"
                      @click="refreshData"
                    ></wd-button>
                  </view>
                  <view class="right-buttons">
                    <wd-button type="primary" size="small" @click="handleSearch">搜索</wd-button>
                    <wd-button plain size="small" @click="resetSearch">重置</wd-button>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 用户列表 -->
          <view class="user-list">
            <wd-table :data="userList" :height="300" :stripe="true" :border="true">
              <!-- 用户名列（固定） -->
              <wd-table-col
                prop="username"
                label="用户名"
                :width="120"
                sortable
                fixed
              ></wd-table-col>

              <!-- 真实姓名列 -->
              <wd-table-col prop="real_name" label="真实姓名" :width="120" sortable></wd-table-col>

              <!-- 邮箱列 -->
              <wd-table-col prop="email" label="邮箱" :width="200" sortable>
                <template #value="{ row }">
                  <text>{{ row.email || '-' }}</text>
                </template>
              </wd-table-col>

              <!-- 电话列 -->
              <wd-table-col prop="phone" label="电话" :width="140" align="center"></wd-table-col>

              <!-- 性别列 -->
              <wd-table-col prop="gender_desc" label="性别" :width="80" align="center">
                <template #value="{ row }">
                  <text class="gender-tag">{{ row.gender_desc }}</text>
                </template>
              </wd-table-col>

              <!-- 状态列 -->
              <wd-table-col prop="status_desc" label="状态" :width="100" align="center">
                <template #value="{ row }">
                  <text class="status-tag" :class="getStatusClass(row.status)">
                    {{ row.status_desc }}
                  </text>
                </template>
              </wd-table-col>

              <!-- 创建时间列 -->
              <wd-table-col
                prop="created_date"
                label="创建时间"
                :width="160"
                align="center"
              ></wd-table-col>

              <!-- 操作列 -->
              <wd-table-col prop="actions" label="操作" :width="80" align="center">
                <template #value="{ row }">
                  <view class="table-actions">
                    <wd-button
                      type="icon"
                      custom-class="more-btn"
                      @click.stop="showMoreActions(row)"
                    >
                      <text class="iconfont-sys iconsys-gengduo"></text>
                    </wd-button>
                  </view>
                </template>
              </wd-table-col>
            </wd-table>

            <!-- 分页组件 -->
            <view class="pagination-wrapper">
              <wd-pagination
                v-model="currentPage"
                :total="totalCount"
                :page-size="pageSize"
                @change="handlePageChange"
                show-icon
                show-message
                :hide-if-one-page="true"
              />
            </view>

            <!-- 空状态 -->
            <view v-if="userList.length === 0 && !loading" class="empty-state">
              <text class="empty-text">暂无商户用户数据</text>
            </view>

            <!-- 加载状态 -->
            <view v-if="loading" class="loading-state">
              <text class="loading-text">加载中...</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作弹出层 -->
    <wd-popup
      v-model="showActionPopup"
      position="bottom"
      :safe-area-inset-bottom="true"
      custom-style="border-radius: 16px 16px 0 0; padding: 0;"
      @close="closeActionPopup"
    >
      <view class="action-popup-content">
        <!-- 标题栏 -->
        <view class="popup-header">
          <text class="popup-title">用户操作</text>
          <view class="popup-close" @click="closeActionPopup">
            <wd-icon name="close" size="20" color="#999" />
          </view>
        </view>

        <!-- 操作按钮列表 -->
        <view class="popup-actions">
          <view class="action-grid">
            <view class="action-item" @click="viewCurrentUser">
              <text class="action-text">查看详情</text>
            </view>

            <view class="action-item" @click="editCurrentUser">
              <text class="action-text">编辑用户</text>
            </view>

            <view class="action-item" @click="toggleCurrentUserStatus">
              <text class="action-text">
                {{ currentUser ? getStatusActionText(currentUser.status) : '切换状态' }}
              </text>
            </view>

            <view 
              class="action-item delete-item" 
              @click="deleteCurrentUser"
              v-if="currentUser && currentUser.username !== 'admin'"
            >
              <text class="action-text delete-text">删除用户</text>
            </view>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  getSystemMerchantUsersApi,
  changeMerchantUserStatusApi,
  deleteSystemMerchantUserApi,
  type ISysMerchantUserListResponse,
  type ISysMerchantUserPageRequest,
} from '@/api/sys/systemMerchantUserApi'
import {
  SysMerchantUserStatus,
  SysMerchantUserStatusMap,
  SysMerchantUserGender,
  SysMerchantUserGenderMap,
  SysMerchantUserValidation,
} from '@/api/sys/constants/systemMerchantUser'
import { onShow } from '@dcloudio/uni-app'

defineOptions({
  name: 'MerchantUserManagement',
})

// 搜索参数
const searchParams = reactive<ISysMerchantUserPageRequest>({
  keyword: '',
  real_name: '',
  status: null,
  gender: null,
  page: 1,
  page_size: 10,
})

const searchStatusValue = ref('')
const searchGenderValue = ref('')

// 状态选项
const statusOptions = ref([
  { label: '全部状态', value: '' },
  {
    label: SysMerchantUserStatusMap[SysMerchantUserStatus.ENABLED],
    value: SysMerchantUserStatus.ENABLED,
  },
  {
    label: SysMerchantUserStatusMap[SysMerchantUserStatus.DISABLED],
    value: SysMerchantUserStatus.DISABLED,
  },
  {
    label: SysMerchantUserStatusMap[SysMerchantUserStatus.LOCKED],
    value: SysMerchantUserStatus.LOCKED,
  },
])

// 性别选项
const genderOptions = ref([
  { label: '全部性别', value: '' },
  {
    label: SysMerchantUserGenderMap[SysMerchantUserGender.MALE],
    value: SysMerchantUserGender.MALE,
  },
  {
    label: SysMerchantUserGenderMap[SysMerchantUserGender.FEMALE],
    value: SysMerchantUserGender.FEMALE,
  },
  {
    label: SysMerchantUserGenderMap[SysMerchantUserGender.UNKNOWN],
    value: SysMerchantUserGender.UNKNOWN,
  },
])

// 数据状态
const userList = ref<ISysMerchantUserListResponse[]>([])
const totalCount = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const loading = ref(false)

// 首次加载标记
const isFirstLoad = ref(true)

// 弹出层相关状态
const showActionPopup = ref(false)
const currentUser = ref<ISysMerchantUserListResponse | null>(null)

// 获取状态样式类
const getStatusClass = (status: number) => {
  switch (status) {
    case SysMerchantUserStatus.ENABLED:
      return 'status-enabled'
    case SysMerchantUserStatus.DISABLED:
      return 'status-disabled'
    case SysMerchantUserStatus.LOCKED:
      return 'status-locked'
    default:
      return ''
  }
}

// 获取状态操作文本
const getStatusActionText = (status: number) => {
  switch (status) {
    case SysMerchantUserStatus.ENABLED:
      return '禁用'
    case SysMerchantUserStatus.DISABLED:
    case SysMerchantUserStatus.LOCKED:
      return '启用'
    default:
      return '操作'
  }
}

// API调用函数
const fetchMerchantUsers = async () => {
  loading.value = true
  try {
    const params: ISysMerchantUserPageRequest = {
      ...searchParams,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    // 关键字智能搜索处理
    if (params.keyword && params.keyword.trim()) {
      const keyword = params.keyword.trim()
      if (SysMerchantUserValidation.PHONE_PATTERN.test(keyword)) {
        params.phone = keyword
      } else if (SysMerchantUserValidation.EMAIL_PATTERN.test(keyword)) {
        params.email = keyword
      } else {
        params.username = keyword
      }
    }
    // 删除原始keyword字段，避免后端冲突
    delete params.keyword

    // 清理空值参数
    Object.keys(params).forEach((key) => {
      const typedKey = key as keyof ISysMerchantUserPageRequest
      if (params[typedKey] === '' || params[typedKey] === null || params[typedKey] === undefined) {
        delete params[typedKey]
      }
    })

    const response = await getSystemMerchantUsersApi(params)
    userList.value = response.data.items
    totalCount.value = response.data.total
  } catch (error) {
    console.error('获取商户用户数据失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchMerchantUsers()
}

// 状态选择器变化事件
const onStatusChange = ({ value }: { value: number | string }) => {
  searchStatusValue.value = value as string
  searchParams.status = value === '' ? null : (value as number)
  handleSearch()
}

// 性别选择器变化事件
const onGenderChange = ({ value }: { value: number | string }) => {
  searchGenderValue.value = value as string
  searchParams.gender = value === '' ? null : (value as number)
  handleSearch()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    keyword: '',
    real_name: '',
    status: null,
    gender: null,
    page: 1,
    page_size: 10,
  })
  searchStatusValue.value = ''
  searchGenderValue.value = ''
  currentPage.value = 1
  fetchMerchantUsers()
}

// 处理分页变化
const handlePageChange = ({ value }: { value: number }) => {
  currentPage.value = value
  fetchMerchantUsers()
}

// 刷新数据
const refreshData = () => {
  fetchMerchantUsers()
}

// 新增用户
const addUser = () => {
  console.log('新增商户用户')
  uni.navigateTo({
    url: '/pages-sys/merchant/user/merchant-user-form?mode=add',
  })
}

// 编辑用户
const editUser = (user: ISysMerchantUserListResponse) => {
  console.log('编辑商户用户:', user)
  uni.navigateTo({
    url: `/pages-sys/merchant/user/merchant-user-form?mode=edit&userId=${user.id}`,
  })
}

// 切换用户状态
const toggleUserStatus = (user: ISysMerchantUserListResponse) => {
  const actionText = getStatusActionText(user.status)
  const nextStatus =
    user.status === SysMerchantUserStatus.ENABLED
      ? SysMerchantUserStatus.DISABLED
      : SysMerchantUserStatus.ENABLED

  uni.showModal({
    title: '确认操作',
    content: `确定要${actionText}用户 "${user.username}" 吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          await changeMerchantUserStatusApi(user.id, { status: nextStatus })
          uni.showToast({
            title: `${actionText}成功`,
            icon: 'success',
          })
          fetchMerchantUsers()
        } catch (error) {
          console.error(`${actionText}失败:`, error)
          uni.showToast({
            title: '操作失败',
            icon: 'error',
          })
        }
      }
    },
  })
}

// 删除用户
const deleteUser = (user: ISysMerchantUserListResponse) => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除用户 "${user.username}" 吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          await deleteSystemMerchantUserApi(user.id)
          uni.showToast({
            title: '删除成功',
            icon: 'success',
          })
          fetchMerchantUsers()
        } catch (error) {
          console.error('删除失败:', error)
          uni.showToast({
            title: '删除失败',
            icon: 'error',
          })
        }
      }
    },
  })
}

// 查看用户详情
const viewUser = (user: ISysMerchantUserListResponse) => {
  console.log('查看商户用户详细:', user)
  uni.navigateTo({
    url: `/pages-sys/merchant/user/merchant-user-form?mode=view&userId=${user.id}`,
  })
}

// 显示更多操作
const showMoreActions = (user: ISysMerchantUserListResponse) => {
  currentUser.value = user
  showActionPopup.value = true
}

// 关闭操作弹出层
const closeActionPopup = () => {
  showActionPopup.value = false
  currentUser.value = null
}

// 查看当前用户
const viewCurrentUser = () => {
  if (currentUser.value) {
    viewUser(currentUser.value)
    closeActionPopup()
  }
}

// 编辑当前用户
const editCurrentUser = () => {
  if (currentUser.value) {
    editUser(currentUser.value)
    closeActionPopup()
  }
}

// 切换当前用户状态
const toggleCurrentUserStatus = () => {
  if (currentUser.value) {
    toggleUserStatus(currentUser.value)
    closeActionPopup()
  }
}

// 删除当前用户
const deleteCurrentUser = () => {
  if (currentUser.value) {
    deleteUser(currentUser.value)
    closeActionPopup()
  }
}

// 返回功能
const handleBack = () => {
  uni.navigateBack()
}

onMounted(() => {
  console.log('商户用户管理页面挂载完成')
  fetchMerchantUsers()
  isFirstLoad.value = false // 标记首次加载完成
})

// 页面显示时刷新数据（从其他页面返回时）
onShow(() => {
  console.log('商户用户管理页面显示')
  // 只有非首次加载时才刷新数据（即从其他页面返回时）
  if (!isFirstLoad.value) {
    console.log('从其他页面返回，刷新商户用户数据')
    fetchMerchantUsers()
  }
})
</script>

<style lang="scss" scoped>
.user-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

// 标题文字设为白色
:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

// 自定义返回按钮样式
:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  background-color: transparent;
  overflow: hidden;
  position: relative;
}

.main-layout {
  flex: 1;
  background-color: white;
  overflow-y: auto;
  padding: 20px;
}

.user-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.action-bar {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
  padding: 0 0 15px 0;
  border-bottom: 1px solid #eee;
}

.top-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-bar {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.search-row {
  display: flex;
  gap: 20px;
  align-items: flex-end;
}

.search-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1px;
}

.search-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
  min-width: 45px;
}

.search-input-component {
  width: 120px;
  height: 28px;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  background-color: white !important;
  overflow: hidden;
}

:deep(.search-input-component .wd-input) {
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

:deep(.search-input-component .wd-input__inner) {
  padding: 6px 10px;
  font-size: 12px;
  height: 28px;
  box-sizing: border-box;
  border: none;
  background-color: transparent;
}

:deep(.search-input-component .wd-input:focus-within) {
  border-color: #4285f4;
}

:deep(.search-input-component .wd-input__inner:focus) {
  outline: none;
}

.picker-text {
  font-size: 12px;
}

.button-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.right-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

// 表格行样式 - 使用更通用的选择器避免tr标签选择器
:deep(.wd-table .wd-table__body .wd-table__row) {
  transition: background-color 0.2s;
}

:deep(.wd-table .wd-table__body .wd-table__row:hover) {
  background-color: #f5f7fa !important;
}

.pagination-wrapper {
  padding: 15px 0;
}

.gender-tag {
  background-color: #f3e5f5;
  color: #7b1fa2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;
}

.status-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;

  &.status-enabled {
    background-color: #e8f5e8;
    color: #4caf50;
  }

  &.status-disabled {
    background-color: #ffebee;
    color: #f44336;
  }

  &.status-locked {
    background-color: #fff3e0;
    color: #ff9800;
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-text {
  color: #999;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.loading-state {
  text-align: center;
  padding: 40px 20px;
}

.loading-text {
  color: #666;
  font-size: 14px;
}

.table-actions {
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.more-btn) {
  width: 32px !important;
  height: 32px !important;
  padding: 0 !important;
  min-width: 32px !important;
  border-radius: 50% !important;
  background-color: #f5f5f5 !important;
  border: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  &:hover {
    background-color: #eeeeee !important;
  }

  .iconfont-sys {
    color: #666666 !important;
    font-size: 14px !important;
    margin: 0 !important;
  }
}

// 弹出层样式
.action-popup-content {
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;

  .popup-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
  }

  .popup-close {
    padding: 8px;
    cursor: pointer;
  }
}

.popup-actions {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow-y: auto;
  background-color: white;
}

.action-grid {
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  justify-content: space-between;
}

.action-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 8px;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
  background-color: #f9f9f9;
  flex: 1;
  min-width: 60px;
  transition: background-color 0.2s;

  &:active {
    background-color: #f0f0f0;
  }

  &:hover {
    background-color: #f0f0f0;
  }
}

.action-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  text-align: center;
}

// 删除操作样式
.delete-item {
  &.action-item {
    border-color: #e0e0e0;
    background-color: #f9f9f9;

    &:active {
      background-color: #f0f0f0;
    }

    &:hover {
      background-color: #fff5f5;
    }
  }
}

.delete-text {
  color: #f44336;
  font-weight: 500;
}
</style>
