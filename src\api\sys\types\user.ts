/**
 * 用户分页查询请求参数
 */
export interface ISysUserPageRequest {
  // 当前页码 (1开始)
  page?: number | null
  // 每页大小
  page_size?: number | null
  // 用户名关键字搜索
  username?: string | null
  // 真实姓名关键字搜索
  real_name?: string | null
  // 邮箱关键字搜索
  email?: string | null
  // 手机号关键字搜索
  phone?: string | null
  // 状态过滤 1:启用 2:禁用 3:锁定
  status?: number | null
  // 性别过滤 1:男 2:女 3:未知
  gender?: number | null
  // 创建时间开始 (YYYY-MM-DD格式)
  created_start?: string | null
  // 创建时间结束 (YYYY-MM-DD格式)
  created_end?: string | null
}

/**
 * 用户列表响应VO (用于分页查询)
 */
export interface ISysUserListResponse {
  // 用户ID
  id: string
  // 用户名
  username: string
  // 头像
  avatar?: string | null
  // 真实姓名
  real_name?: string | null
  // 手机号 (脱敏)
  phone?: string | null
  // 邮箱 (脱敏)
  email?: string | null
  // 性别 1:男 2:女 3:未知
  gender?: number | null
  // 状态 1:启用 2:禁用 3:锁定
  status: number
  // 最后登录时间
  last_login_date?: string | null
  // 最后登录IP
  last_login_ip?: string | null
  // 创建时间
  created_date: string
  // 更新时间
  updated_date: string
}

// 未分配角色的用户数量
export interface ISysUnassignedUserCountResponse {
  /// 未分配角色的用户总数量
  unassigned_user_count: number,
}

/**
 * 用户角色详情VO
 */
export interface ISysUserRoleDetailVo {
  // 角色ID
  id: string
  // 角色名称
  name: string
  // 角色描述
  description?: string | null
  // 创建时间
  created_date: string
  // 更新时间
  updated_date: string
  // 备注
  remark?: string | null
}

/**
 * 用户详情响应VO (包含完整信息)
 */
export interface ISysUserDetailResponse {
  // 用户ID
  id: string
  // 用户名
  username: string
  // 真实姓名
  real_name?: string | null
  // 手机号
  phone?: string | null
  // 邮箱
  email?: string | null
  // 头像地址
  avatar?: string | null
  // 性别 1:男 2:女 3:未知
  gender?: number | null
  // 状态 1:启用 2:禁用 3:锁定
  status: number
  // 最后登录时间
  last_login_date?: string | null
  // 最后登录IP
  last_login_ip?: string | null
  // 创建时间
  created_date: string
  // 更新时间
  updated_date: string
  // 创建人ID
  created_by?: string | null
  // 更新人ID
  updated_by?: string | null
  // 备注
  remark?: string | null
  // 用户角色详情列表
  roles: ISysUserRoleDetailVo[]
}

/**
 * 用户创建请求DTO
 */
export interface ISysUserCreateRequest {
  // 用户名
  username: string
  // 密码
  password: string
  // 真实姓名
  real_name?: string | null
  // 手机号
  phone?: string | null
  // 邮箱
  email?: string | null
  // 头像地址
  avatar?: string | null
  // 性别 1:男 2:女 3:未知
  gender?: number | null
  // 状态 1:启用 2:禁用 3:锁定 (默认启用)
  status?: number | null
  // 角色ID集合
  role_ids?: string[] | null
  // 备注
  remark?: string | null
}

/**
 * 用户更新请求DTO
 */
export interface ISysUserUpdateRequest {
  // 真实姓名
  real_name?: string | null
  // 手机号
  phone?: string | null
  // 邮箱
  email?: string | null
  // 头像地址
  avatar?: string | null
  // 性别 1:男 2:女 3:未知
  gender?: number | null
  // 状态 1:启用 2:禁用 3:锁定
  status?: number | null
  // 角色ID集合
  role_ids?: string[] | null
  // 备注
  remark?: string | null
}

/**
 * 用户状态切换请求DTO
 */
export interface ISysUserStatusRequest {
  // 状态 1:启用 2:禁用 3:锁定
  status: number
}

/**
 * 用户重置密码请求DTO
 */
export interface ISysUserResetPasswordRequest {
  // 新密码
  new_password: string
}

/**
 * 批量删除用户请求DTO
 */
export interface ISysUserBatchDeleteRequest {
  // 用户ID集合
  user_ids: string[]
}

/**
 * 检查用户名是否存在请求参数
 */
export interface ISysUserCheckUsernameRequest {
  // 要检查的用户名
  username: string
  // 排除的用户ID（用于更新时检查）
  exclude_id?: string
}

/**
 * 检查邮箱是否存在请求参数
 */
export interface ISysUserCheckEmailRequest {
  // 要检查的邮箱
  email: string
  // 排除的用户ID（用于更新时检查）
  exclude_id?: string
}

/**
 * 检查手机号是否存在请求参数
 */
export interface ISysUserCheckPhoneRequest {
  // 要检查的手机号
  phone: string
  // 排除的用户ID（用于更新时检查）
  exclude_id?: string
}
