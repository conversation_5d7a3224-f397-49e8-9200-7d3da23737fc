<template>
  <view class="goods-list">
    <!-- 商品列表 -->
    <view class="goods-container">
      <view
        class="goods-item"
        v-for="item in goodsList"
        :key="item.id"
        @click="handleGoodsClick(item)"
      >
        <!-- 左侧图片 -->
        <view class="goods-image-container">
          <image v-if="item.image" class="goods-image" :src="item.image" mode="aspectFill" />
          <view
            v-else
            class="goods-image-placeholder"
            :style="{ backgroundColor: item.color }"
          ></view>
        </view>

        <!-- 右侧商品信息 -->
        <view class="goods-info">
          <text class="goods-title">{{ item.title }}</text>

          <view class="goods-price" v-if="showPrice">
            <text class="current-price">¥{{ item.price }}</text>
            <text v-if="item.originalPrice" class="original-price">¥{{ item.originalPrice }}</text>
          </view>

          <view class="goods-bottom">
            <text class="goods-sales">已售{{ item.sales }}件</text>
            <view class="goods-like" @click.stop="handleLikeClick(item)">
              <wd-icon name="like" :color="item.isLiked ? '#ff4757' : '#ccc'" size="16px" />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading || !hasMore">
      <view v-if="loading" class="loading-item">
        <wd-loading size="20px" />
        <text class="loading-text">{{ loadingText }}</text>
      </view>
      <view v-else-if="!hasMore" class="no-more-item">
        <text class="no-more-text">{{ noMoreText }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
// 定义商品数据类型
interface GoodsItem {
  id: number | string
  title: string
  price?: number
  originalPrice?: number | null
  color?: string
  sales: number
  isLiked: boolean
  image?: string
}

// 定义组件属性
interface Props {
  // 商品列表
  goodsList: GoodsItem[]
  // 是否显示价格
  showPrice?: boolean
  // 是否正在加载
  loading?: boolean
  // 是否还有更多数据
  hasMore?: boolean
  // 加载文本
  loadingText?: string
  // 没有更多数据文本
  noMoreText?: string
}

// 定义事件
interface Emits {
  // 商品点击事件
  (e: 'goods-click', item: GoodsItem): void
  // 点赞事件
  (e: 'like-click', item: GoodsItem): void
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  showPrice: true,
  loading: false,
  hasMore: true,
  loadingText: '加载中...',
  noMoreText: '没有更多商品了',
})

const emit = defineEmits<Emits>()

// 商品点击处理
const handleGoodsClick = (item: GoodsItem) => {
  emit('goods-click', item)
}

// 点赞点击处理
const handleLikeClick = (item: GoodsItem) => {
  emit('like-click', item)
}
</script>

<style lang="scss" scoped>
.goods-list {
  width: 100%;
}

// 商品容器
.goods-container {
  display: flex;
  flex-direction: column;
  gap: 0;
}

// 商品项
.goods-item {
  background: transparent;
  border-radius: 0;
  padding: 4px 6px;
  display: flex;
  gap: 10px;
  transition: background-color 0.1s;
}

// 左侧图片容器
.goods-image-container {
  width: 120px;
  height: 120px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
}

.goods-image {
  width: 100%;
  height: 100%;
  display: block;
}

.goods-image-placeholder {
  width: 100%;
  height: 100%;
  display: block;
}

// 右侧商品信息
.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 120px;
  padding: 0;
}

.goods-title {
  font-size: 15px;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  font-weight: 500;
  margin-bottom: 8px;
}

.goods-price {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 8px;
}

.current-price {
  font-size: 18px;
  color: #ff4757;
  font-weight: 600;
}

.original-price {
  font-size: 13px;
  color: #999;
  text-decoration: line-through;
}

.goods-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.goods-sales {
  font-size: 13px;
  color: #999;
}

.goods-like {
  padding: 4px;
  transition: transform 0.2s;

  &:active {
    transform: scale(0.9);
  }
}

// 加载状态样式
.loading-container {
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.no-more-item {
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-more-text {
  font-size: 14px;
  color: #999;
  padding: 10px 0;
}
</style> 