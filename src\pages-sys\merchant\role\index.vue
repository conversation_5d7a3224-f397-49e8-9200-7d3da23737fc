<route lang="json5">
  {
    style: {
      navigationStyle: 'custom',
      navigationBarTitleText: '商户角色管理',
    },
    access: {
      requireAuth: true,
    },
  }
  </route>

  <template>
  <view class="role-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      title="商户角色管理"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <!-- 顶部搜索区域 -->
      <view class="top-search-area">
        <!-- 操作栏 -->
        <view class="action-bar">
          <!-- 搜索条件栏 -->
          <view class="search-bar">
            <view class="search-form">
              <!-- 第一行搜索条件 - 始终显示 -->
              <view class="search-row">
                <view class="search-item">
                  <text class="search-label">商户名称：</text>
                  <wd-input
                    class="search-input-component"
                    placeholder="请输入商户名称"
                    v-model="searchParams.merchant_name"
                    clearable
                    @clear="handleSearch"
                  />
                </view>
                <view class="search-item">
                  <text class="search-label">角色编码：</text>
                  <wd-input
                    class="search-input-component"
                    placeholder="请输入角色编码"
                    v-model="searchParams.role_code"
                    clearable
                    @clear="handleSearch"
                  />
                </view>
              </view>

              <!-- 可折叠的搜索条件区域 -->
              <view class="collapsible-search" :class="{ expanded: searchExpanded }">
                <!-- 第二行搜索条件 -->
                <view class="search-row">
                  <view class="search-item">
                    <text class="search-label">角色名称：</text>
                    <wd-input
                      class="search-input-component"
                      placeholder="请输入角色名称"
                      v-model="searchParams.role_name"
                      clearable
                      @clear="handleSearch"
                    />
                  </view>
                </view>

                <!-- 第三行搜索条件 -->
                <view class="search-row">
                  <view class="search-item">
                    <text class="search-label">角色类型：</text>
                    <wd-select-picker
                      class="search-select-picker"
                      v-model="searchParams.role_type"
                      :columns="roleTypeOptions"
                      type="radio"
                      :show-confirm="false"
                      @change="onRoleTypeChange"
                    />
                  </view>
                  <view class="search-item">
                    <text class="search-label">状态：</text>
                    <wd-select-picker
                      class="search-select-picker"
                      v-model="searchParams.status"
                      :columns="statusOptions"
                      type="radio"
                      :show-confirm="false"
                      @change="onStatusChange"
                    />
                  </view>
                </view>

                <!-- 第四行搜索条件 -->
                <view class="search-row">
                  <view class="search-item">
                    <text class="search-label">数据范围：</text>
                    <wd-select-picker
                      class="search-select-picker"
                      v-model="searchParams.data_scope"
                      :columns="dataScopeOptions"
                      type="radio"
                      :show-confirm="false"
                      @change="onDataScopeChange"
                    />
                  </view>
                  <view class="search-item">
                    <text class="search-label">默认角色：</text>
                    <wd-select-picker
                      class="search-select-picker"
                      v-model="searchParams.is_default"
                      :columns="defaultOptions"
                      type="radio"
                      :show-confirm="false"
                      @change="onDefaultChange"
                    />
                  </view>
                </view>
              </view>

              <!-- 展开/收起按钮 - 始终在搜索条件底部 -->
              <view class="search-toggle-bottom" @click="toggleSearchExpanded">
                <text
                  class="iconfont-sys iconsys-xia2"
                  :class="{ expanded: searchExpanded }"
                ></text>
              </view>

              <!-- 按钮行 - 始终显示在下方 -->
              <view class="button-row">
                <view class="left-buttons">
                  <wd-button type="success" size="small" @click="addRole">新增</wd-button>
                  <wd-button
                    type="info"
                    size="small"
                    icon="refresh"
                    @click="refreshData"
                  ></wd-button>
                </view>
                <view class="right-buttons">
                  <wd-button type="primary" size="small" @click="handleSearch">搜索</wd-button>
                  <wd-button plain size="small" @click="resetSearch">重置</wd-button>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 主体内容区域 -->
      <view class="main-content-area">
        <!-- 左侧商户侧边栏 -->
        <view class="left-sidebar">
          <view class="sidebar-header">
            <text class="sidebar-title">商户列表</text>
          </view>

          <!-- 商户侧边栏 -->
          <wd-sidebar
            v-model="selectedMerchantIndex"
            @change="handleMerchantChange"
            custom-class="merchant-sidebar"
          >
            <wd-sidebar-item
              v-for="(merchant, index) in merchantList"
              :key="merchant.id"
              :value="index"
              :label="merchant.merchant_name"
            />
          </wd-sidebar>

          <!-- 商户加载状态 -->
          <view v-if="merchantLoading" class="merchant-loading">
            <wd-loading />
            <text class="loading-text">加载商户列表...</text>
          </view>

          <!-- 商户空状态 -->
          <view v-else-if="merchantList.length === 0" class="merchant-empty">
            <text class="empty-text">暂无商户数据</text>
          </view>
        </view>

        <!-- 右侧内容区域 -->
        <view class="right-content">
          <!-- 角色管理区域 -->
          <view v-if="selectedMerchant" class="role-management-area">
            <!-- 角色列表 -->
            <view class="role-list-container">
              <!-- 加载状态 -->
              <view v-if="roleLoading" class="loading-state">
                <wd-loading />
                <text class="loading-text">加载角色数据...</text>
              </view>

              <!-- 角色列表 -->
              <view v-else-if="roleList.length > 0" class="role-list-wrapper">
                <view
                  v-for="role in roleList"
                  :key="role.id"
                  class="role-item"
                  @click="viewRole(role)"
                >
                  <!-- 角色标题部分 - 角色名称和类型标签同一行 -->
                  <view class="role-header">
                    <view class="role-title-info">
                      <text class="role-name">{{ role.role_name }}</text>
                      <text class="type-tag" :class="getRoleTypeClass(role.role_type)">
                        {{ role.role_type_desc }}
                      </text>
                    </view>
                    <view class="role-actions" @click.stop="">
                      <wd-button
                        type="icon"
                        custom-class="action-btn"
                        @click.stop="showActionSheet(role)"
                      >
                        <text class="iconfont-sys iconsys-gengduo"></text>
                      </wd-button>
                    </view>
                  </view>

                  <!-- 角色信息部分 - 状态标签和默认角色标签同一行 -->
                  <view class="role-info-section">
                    <view class="role-tags">
                      <text class="status-tag" :class="getRoleStatusClass(role.status)">
                        {{ role.status_desc }}
                      </text>
                      <text
                        class="default-tag"
                        :class="{ 'is-default': role.is_default }"
                        v-if="role.is_default"
                      >
                        默认角色
                      </text>
                    </view>
                    <!-- 详细信息行 - 水平排列 -->
                    <view class="role-details-row">
                      <text class="detail-item">{{ role.data_scope_desc }}</text>
                      <text class="detail-item">用户数：{{ role.user_count || 0 }}</text>
                      <text class="detail-item">权限数：{{ role.permission_count || 0 }}</text>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 分页组件 -->
              <view class="pagination-wrapper" v-if="roleList.length > 0">
                <wd-pagination
                  v-model="rolePagination.current"
                  :total="rolePagination.total"
                  :page-size="rolePagination.pageSize"
                  @change="handleRolePageChange"
                  show-icon
                  show-message
                  :hide-if-one-page="true"
                />
              </view>

              <!-- 空状态 -->
              <view v-else-if="!roleLoading" class="empty-state">
                <wd-status-tip type="search" tip="该商户暂无角色数据" />
              </view>
            </view>
          </view>

          <!-- 未选择商户提示 -->
          <view v-else class="empty-content">
            <wd-status-tip type="search" tip="请选择左侧商户查看角色信息" />
          </view>
        </view>
      </view>

      <!-- 底部操作菜单 -->
      <wd-popup
        v-model="showActionSheetVisible"
        position="bottom"
        :safe-area-inset-bottom="true"
        custom-style="border-radius: 16px 16px 0 0; padding: 0;"
        @close="handleActionCancel"
      >
        <view class="role-popup-content">
          <!-- 标题栏 -->
          <view class="popup-header">
            <text class="popup-title">角色操作</text>
            <view class="popup-close" @click="handleActionCancel">
              <wd-icon name="close" size="20" color="#999" />
            </view>
          </view>

          <!-- 操作功能区 -->
          <view class="popup-actions">
            <!-- 第一行操作 -->
            <view class="action-grid">
              <view class="action-item" @click="viewRole(selectedRole)">
                <text class="action-text">查看详情</text>
              </view>

              <view class="action-item" @click="editRole(selectedRole)">
                <text class="action-text">编辑角色</text>
              </view>

              <view class="action-item" @click="manageRolePermission(selectedRole)">
                <text class="action-text">角色权限</text>
              </view>

              <view class="action-item delete-item" @click="deleteRole(selectedRole)">
                <text class="action-text delete-text">删除角色</text>
              </view>
            </view>
          </view>
        </view>
      </wd-popup>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { getSystemMerchantBasicListApi } from '@/api/sys/systemMerchantApi'
import type { ISysMerchantBasicResponse } from '@/api/sys/types/merchant'
import {
  getSystemMerchantRolesApi,
  deleteSystemMerchantRoleApi,
} from '@/api/sys/systemMerchantRoleApi'
import type {
  ISysMerchantRolePageRequest,
  ISysMerchantRoleListResponse,
} from '@/api/sys/types/merchantRole'

defineOptions({
  name: 'MerchantRoleManagement',
})

// 搜索参数
const searchParams = ref({
  merchant_name: '',
  role_code: '',
  role_name: '',
  role_type: '',
  status: '',
  is_default: '',
  data_scope: '',
  page: 1,
  page_size: 25,
})

// 角色类型选项
const roleTypeOptions = ref([
  { label: '全部', value: '' },
  { label: '系统定义角色', value: 1 },
  { label: '商户定义角色', value: 2 },
])

// 状态选项 - 0禁用 1启用
const statusOptions = ref([
  { label: '全部', value: '' },
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
])

// 默认角色选项
const defaultOptions = ref([
  { label: '全部', value: '' },
  { label: '是', value: true },
  { label: '否', value: false },
])

// 数据范围选项
const dataScopeOptions = ref([
  { label: '全部', value: '' },
  { label: '商户全部数据', value: 1 },
  { label: '个人数据', value: 2 },
])

// 商户数据状态
const merchantList = ref<ISysMerchantBasicResponse[]>([])
const selectedMerchantIndex = ref<number>(0)
const merchantLoading = ref(false)

// 角色数据状态
const roleList = ref<ISysMerchantRoleListResponse[]>([])
const roleLoading = ref(false)
const rolePagination = ref({
  current: 1,
  total: 0,
  pageSize: 10,
})

// 底部操作菜单状态
const showActionSheetVisible = ref(false)
const selectedRole = ref<ISysMerchantRoleListResponse | null>(null)

// 搜索条件折叠状态
const searchExpanded = ref(false)

// 计算当前选中的商户
const selectedMerchant = computed(() => {
  if (selectedMerchantIndex.value >= 0 && merchantList.value.length > 0) {
    return merchantList.value[selectedMerchantIndex.value]
  }
  return null
})

// 获取商户基础信息列表
const fetchMerchantList = async () => {
  merchantLoading.value = true
  try {
    const params = searchParams.value.merchant_name
      ? { merchant_name: searchParams.value.merchant_name }
      : undefined

    const result = await getSystemMerchantBasicListApi(params)

    if (result.code === 200 && result.data) {
      merchantList.value = result.data

      console.log('获取商户列表成功:', result.data)

      // 如果有商户数据，默认选择第一个
      if (merchantList.value.length > 0) {
        selectedMerchantIndex.value = 0
      } else {
        selectedMerchantIndex.value = -1
      }
    } else {
      throw new Error(result.message || '获取商户列表失败')
    }
  } catch (error) {
    console.error('获取商户列表失败:', error)
    uni.showToast({
      title: '获取商户列表失败',
      icon: 'none',
    })
  } finally {
    merchantLoading.value = false
  }
}

// 获取商户角色列表
const fetchMerchantRoles = async () => {
  if (!selectedMerchant.value) return

  roleLoading.value = true
  try {
    const params: ISysMerchantRolePageRequest = {
      merchant_id: selectedMerchant.value.id,
      role_code: searchParams.value.role_code || undefined,
      role_name: searchParams.value.role_name || undefined,
      role_type: searchParams.value.role_type ? Number(searchParams.value.role_type) : undefined,
      status: searchParams.value.status ? Number(searchParams.value.status) : undefined,
      is_default: searchParams.value.is_default
        ? Boolean(searchParams.value.is_default)
        : undefined,
      data_scope: searchParams.value.data_scope ? Number(searchParams.value.data_scope) : undefined,
      page: rolePagination.value.current,
      page_size: rolePagination.value.pageSize,
    }

    const result = await getSystemMerchantRolesApi(params)

    if (result.code === 200 && result.data) {
      roleList.value = result.data.items
      rolePagination.value.total = result.data.total

      console.log('获取商户角色列表成功:', result.data)
    } else {
      throw new Error(result.message || '获取商户角色列表失败')
    }
  } catch (error) {
    console.error('获取商户角色列表失败:', error)
    uni.showToast({
      title: '获取角色列表失败',
      icon: 'none',
    })
  } finally {
    roleLoading.value = false
  }
}

// 处理商户选择变化
const handleMerchantChange = ({ value }: { value: number }) => {
  selectedMerchantIndex.value = value
  console.log('选择商户:', merchantList.value[value])
  // 重置角色分页，角色数据加载由watch监听器处理
  rolePagination.value.current = 1
}

// 处理角色分页变化
const handleRolePageChange = ({ value }: { value: number }) => {
  rolePagination.value.current = value
  fetchMerchantRoles()
}

// 获取角色类型样式类
const getRoleTypeClass = (roleType: number) => {
  switch (roleType) {
    case 1:
      return 'type-admin'
    case 2:
      return 'type-custom'
    default:
      return ''
  }
}

// 获取角色状态样式类
const getRoleStatusClass = (status: number) => {
  switch (status) {
    case 1:
      return 'status-enabled'
    case 0:
      return 'status-disabled'
    default:
      return ''
  }
}

// 显示底部操作菜单
const showActionSheet = (role: ISysMerchantRoleListResponse) => {
  selectedRole.value = role
  showActionSheetVisible.value = true
}

// 取消底部操作菜单
const handleActionCancel = () => {
  showActionSheetVisible.value = false
  selectedRole.value = null
  console.log('取消操作')
}

// 查看角色详情
const viewRole = (role: ISysMerchantRoleListResponse | null) => {
  if (!role) return
  console.log('查看角色:', role)

  uni.navigateTo({
    url: `/pages-sys/merchant/role/merchant-role-form?mode=view&roleId=${role.id}&merchantId=${role.merchant_id}`,
  })

  // 关闭弹层
  handleActionCancel()
}

// 编辑角色
const editRole = (role: ISysMerchantRoleListResponse | null) => {
  if (!role) return
  console.log('编辑角色:', role)

  uni.navigateTo({
    url: `/pages-sys/merchant/role/merchant-role-form?mode=edit&roleId=${role.id}&merchantId=${role.merchant_id}`,
  })

  // 关闭弹层
  handleActionCancel()
}

// 删除角色
const deleteRole = (role: ISysMerchantRoleListResponse | null) => {
  if (!role) return
  uni.showModal({
    title: '确认删除',
    content: `确定要删除角色 "${role.role_name}" 吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          await deleteSystemMerchantRoleApi(role.id)
          uni.showToast({
            title: '删除成功',
            icon: 'success',
          })
          // 刷新角色列表
          fetchMerchantRoles()
          // 关闭弹层
          handleActionCancel()
        } catch (error) {
          console.error('删除角色失败:', error)
          uni.showToast({
            title: '删除失败',
            icon: 'error',
          })
        }
      }
    },
  })
}

// 处理搜索 - 用于顶部商户名称搜索
const handleSearch = async () => {
  console.log('搜索商户:', searchParams.value.merchant_name)
  // 重新加载商户列表
  await fetchMerchantList()
  // 重置角色分页，角色数据加载由watch监听器处理
  if (selectedMerchant.value) {
    rolePagination.value.current = 1
  }
}

// 角色类型选择器变化事件
const onRoleTypeChange = ({ value }: { value: number | string }) => {
  console.log('角色类型变化:', value)
  searchParams.value.role_type = value as any
  // 重置分页并触发角色数据重新加载
  if (selectedMerchant.value) {
    rolePagination.value.current = 1
    fetchMerchantRoles()
  }
}

// 状态选择器变化事件
const onStatusChange = ({ value }: { value: number | string }) => {
  console.log('状态变化:', value)
  searchParams.value.status = value as any
  // 重置分页并触发角色数据重新加载
  if (selectedMerchant.value) {
    rolePagination.value.current = 1
    fetchMerchantRoles()
  }
}

// 默认角色选择器变化事件
const onDefaultChange = ({ value }: { value: boolean | string }) => {
  console.log('默认角色变化:', value)
  searchParams.value.is_default = value as any
  // 重置分页并触发角色数据重新加载
  if (selectedMerchant.value) {
    rolePagination.value.current = 1
    fetchMerchantRoles()
  }
}

// 数据范围选择器变化事件
const onDataScopeChange = ({ value }: { value: number | string }) => {
  console.log('数据范围变化:', value)
  searchParams.value.data_scope = value as any
  // 重置分页并触发角色数据重新加载
  if (selectedMerchant.value) {
    rolePagination.value.current = 1
    fetchMerchantRoles()
  }
}

// 重置搜索 - 用于顶部搜索
const resetSearch = async () => {
  searchParams.value = {
    merchant_name: '',
    role_code: '',
    role_name: '',
    role_type: '',
    status: '',
    is_default: '',
    data_scope: '',
    page: 1,
    page_size: 25,
  }
  // 重新加载商户列表
  await fetchMerchantList()
  // 重置角色分页，角色数据加载由watch监听器处理
  if (selectedMerchant.value) {
    rolePagination.value.current = 1
  }
  uni.showToast({
    title: '搜索条件已重置',
    icon: 'success',
  })
}

// 新增角色
const addRole = () => {
  if (!selectedMerchant.value) {
    uni.showToast({
      title: '请先选择商户',
      icon: 'none',
    })
    return
  }

  console.log('新增商户角色')
  uni.navigateTo({
    url: `/pages-sys/merchant/role/merchant-role-form?mode=add&merchantId=${selectedMerchant.value.id}`,
  })
}

// 刷新数据 - 用于顶部刷新
const refreshData = () => {
  console.log('刷新数据')
  fetchMerchantList()
  // 角色数据加载由watch监听器处理
}

// 返回功能
const handleBack = () => {
  console.log('返回功能')
  uni.navigateBack()
}

// 切换搜索条件折叠状态
const toggleSearchExpanded = () => {
  searchExpanded.value = !searchExpanded.value
}

// 跳转到角色权限管理页面
const manageRolePermission = (role: ISysMerchantRoleListResponse | null) => {
  if (!role) return
  uni.navigateTo({
    url: `/pages-sys/merchant/role/role-permission-manage?roleId=${role.id}`,
  })
  handleActionCancel()
}

// 监听选中商户变化，自动加载角色数据
watch(
  selectedMerchant,
  (newMerchant) => {
    if (newMerchant) {
      rolePagination.value.current = 1
      fetchMerchantRoles()
    } else {
      roleList.value = []
    }
  },
  { immediate: true },
)

onMounted(async () => {
  console.log('商户角色管理页面挂载完成')
  await fetchMerchantList()
})
</script>

<style lang="scss" scoped>
.role-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

// 标题文字设为白色
:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

// 自定义返回按钮样式
:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  background-color: transparent;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

// 顶部搜索区域
.top-search-area {
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  padding: 20px 20px 0 20px;
  flex-shrink: 0;
}

// 主体内容区域
.main-content-area {
  flex: 1;
  display: flex;
  overflow: hidden;
}

// 左侧商户侧边栏
.left-sidebar {
  width: 145px;
  background-color: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  padding: 10px 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  background-color: #fafafa;
}

.sidebar-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

// 商户侧边栏样式
:deep(.merchant-sidebar) {
  flex: 1;
  width: 100% !important;
  max-width: 145px !important;
  min-width: 145px !important;
}

// 侧边栏项目样式 - 左对齐和文本省略
:deep(.merchant-sidebar .wd-sidebar-item) {
  text-align: left !important;
  justify-content: flex-start !important;
  width: 100% !important;
  max-width: 145px !important;
  box-sizing: border-box !important;
}

:deep(.merchant-sidebar .wd-sidebar-item__label) {
  text-align: left !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 100% !important;
  display: block !important;
  width: 100% !important;
  box-sizing: border-box !important;
  padding: 12px 16px !important;
}

:deep(.merchant-sidebar .wd-sidebar-item__content) {
  justify-content: flex-start !important;
  text-align: left !important;
  width: 100% !important;
  overflow: hidden !important;
  max-width: 145px !important;
  box-sizing: border-box !important;
}

// 更全面的文本省略样式
:deep(.merchant-sidebar .wd-sidebar-item .wd-sidebar-item__title) {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 100% !important;
}

// 确保文本元素都不换行 - 移除通配符选择器
:deep(.merchant-sidebar .wd-sidebar-item text) {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

:deep(.merchant-sidebar .wd-sidebar-item view) {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.merchant-loading,
.merchant-empty {
  padding: 40px 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-text,
.empty-text {
  font-size: 14px;
  color: #666;
}

// 右侧内容区域
.right-content {
  flex: 1;
  background-color: white;
  overflow-y: auto;
  padding: 8px;
}

// 角色管理区域
.role-management-area {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// 角色列表容器
.role-list-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

// 角色列表包装器
.role-list-wrapper {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 8px;
}

// 角色列表项样式
.role-item {
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  padding: 16px;
}

// 角色标题部分
.role-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  width: 100%;
}

.role-title-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-grow: 1;
  gap: 12px;
}

.role-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
  flex-shrink: 0;
}

.role-status-info {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

// 角色操作按钮部分
.role-actions {
  display: flex;
  gap: 6px;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  flex-shrink: 0;
}

// 图标按钮样式
:deep(.action-btn) {
  width: 32px !important;
  height: 32px !important;
  min-width: 32px !important;
  border-radius: 50% !important;
  background-color: #f0f0f0 !important;
  border: none !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  .iconfont-sys {
    font-size: 16px !important;
    color: #666 !important;
  }

  &:active {
    background-color: #e0e0e0 !important;
  }
}

// 角色信息部分
.role-info-section {
  display: flex;
  flex-direction: column;
  gap: 6px;
  width: 100%;
}

.role-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  margin-bottom: 4px;
  width: 100%;
}

.role-details-row {
  display: flex;
  gap: 10px; /* Adjust gap for horizontal layout */
  flex-wrap: wrap;
  width: 100%;
}

.detail-item {
  font-size: 12px;
  color: #666;
  line-height: 1.3;
  white-space: nowrap; /* Prevent text from wrapping */
}

// 空白内容
.empty-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-bar {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 10px;
}

.search-bar {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 2px;
  width: 100%;
}

.search-row {
  display: flex;
  gap: 20px;
  align-items: flex-end;
}

.search-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1px;
}

.search-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
  min-width: 45px;
}

.search-input-component {
  width: 120px;
  height: 28px;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  background-color: white !important;
  overflow: hidden;
}

:deep(.search-input-component .wd-input) {
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

:deep(.search-input-component .wd-input__inner) {
  padding: 6px 10px;
  font-size: 12px;
  height: 28px;
  box-sizing: border-box;
  border: none;
  background-color: transparent;
}

:deep(.search-input-component .wd-input:focus-within) {
  border-color: #4285f4;
}

:deep(.search-input-component .wd-input__inner:focus) {
  outline: none;
}

.button-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.right-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.pagination-wrapper {
  padding: 15px 0;
  display: flex;
  justify-content: center;
}

// 角色类型标签
.type-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;

  &.type-admin {
    background-color: #e3f2fd;
    color: #1976d2;
  }

  &.type-custom {
    background-color: #f3e5f5;
    color: #7b1fa2;
  }
}

// 默认角色标签
.default-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;
  background-color: #f5f5f5;
  color: #666;

  &.is-default {
    background-color: #fff3e0;
    color: #f57c00;
  }
}

// 状态标签
.status-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;

  &.status-enabled {
    background-color: #e8f5e8;
    color: #4caf50;
  }

  &.status-disabled {
    background-color: #ffebee;
    color: #f44336;
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.loading-state {
  text-align: center;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

// 底部弹出层样式
.role-popup-content {
  background-color: white;
  border-radius: 16px 16px 0 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%; /* Ensure content takes full height */
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.popup-close {
  padding: 8px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-actions {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow-y: auto;
  background-color: white;
}

.action-grid {
  display: flex;
  flex-wrap: nowrap; /* 保持一行显示 */
  gap: 8px;
  justify-content: space-between;
}

.action-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 12px; /* 略微减少左右padding为文字腾出空间 */
  border-radius: 12px;
  border: 1px solid #e0e0e0;
  background-color: #f9f9f9;
  flex: 1; /* 保持平分宽度 */
  min-width: 60px; /* 设置最小宽度确保文字显示 */

  &:active {
    background-color: #f0f0f0;
  }
}

.action-text {
  font-size: 13px; /* 略微减小字体以适应更小空间 */
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  text-align: center;
  /* 移除截断样式，让文字完整显示 */
}

// 删除操作样式
.delete-item {
  &.action-item {
    border-color: #e0e0e0;
    background-color: #f9f9f9;

    &:active {
      background-color: #f0f0f0;
    }
  }
}

.delete-text {
  color: #f44336;
  font-weight: 500;
}

// 搜索条件折叠样式
.search-toggle-bottom {
  display: flex;
  justify-content: center;
  align-items: center;
  user-select: none;
  .iconfont-sys {
    font-size: 18px;
    color: #666;
    transition: transform 0.3s ease, color 0.3s ease;

    &.expanded {
      transform: rotate(180deg);
    }
  }
}

.collapsible-search {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
  opacity: 0;
  margin-top: 5px;

  &.expanded {
    max-height: 1000px;
    opacity: 1;
  }

  .search-row {
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
