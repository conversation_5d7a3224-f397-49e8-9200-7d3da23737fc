/**
 * 系统角色管理相关常量
 */

/**
 * 分页查询默认参数
 */
export const SysRolePageConstants = {
  // 默认页码
  DEFAULT_PAGE: 1,
  // 默认每页大小
  DEFAULT_PAGE_SIZE: 10,
  // 最大每页大小
  MAX_PAGE_SIZE: 100,
  // 最小每页大小
  MIN_PAGE_SIZE: 1,
} as const


/**
 * 角色验证规则常量
 */
export const SysRoleValidation = {
  // 角色名称
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 20,
  NAME_PATTERN: /^[\u4e00-\u9fa5a-zA-Z0-9_\-\s]{2,20}$/,
} as const

/**
 * 权限类型枚举及映射
 */
export enum PermissionType {
  // 目录
  DIRECTORY = 1,
  // 菜单
  MENU = 2,
  // 按钮
  BUTTON = 3,
}

export const PermissionTypeMap = {
  [PermissionType.DIRECTORY]: '目录',
  [PermissionType.MENU]: '菜单',
  [PermissionType.BUTTON]: '按钮',
} as const