<template>
  <view class="permission-container">
    <view class="permission-header">
      <view class="permission-actions">
        <view class="permission-cascade-switch">
          <text class="switch-label">子级联动</text>
          <wd-switch v-model="cascadeSelection" size="20px" :disabled="readonly" />
        </view>
        <wd-button size="small" @click="expandAll">全部展开</wd-button>
        <wd-button size="small" @click="collapseAll">全部收起</wd-button>
        <wd-button v-if="!readonly" size="small" @click="checkAll">全选</wd-button>
        <wd-button v-if="!readonly" size="small" @click="uncheckAll">取消全选</wd-button>
      </view>
    </view>

    <view class="permission-tree" v-if="permissionTree.length > 0">
      <wd-collapse v-model="expandedPermissions">
        <wd-collapse-item
          v-for="node in permissionTree"
          :key="node.id"
          :name="node.id"
          custom-body-style="padding: 0;"
          :custom-class="'permission-node-lv0'"
        >
          <template #title>
            <view class="permission-node">
              <view class="permission-node-info">
                <view class="checkbox-container" @click.stop.prevent v-if="!readonly">
                  <wd-checkbox
                    :model-value="checkedPermissionIds.includes(node.id)"
                    :indeterminate="getIndeterminateState(node)"
                    @change="(checked) => handlePermissionCheck(node.id, checked)"
                    @click.stop.prevent
                    custom-class="permission-checkbox"
                  />
                </view>
                <!-- 使用wot组件图标 - 没有特殊前缀的图标 -->
                <wd-icon
                  v-if="node.icon && !node.icon.startsWith('iconsys')"
                  :name="node.icon"
                  size="16"
                  custom-class="permission-node-icon"
                />
                <!-- 使用自定义图标 - iconsys开头的图标 -->
                <view
                  v-else-if="node.icon && node.icon.startsWith('iconsys')"
                  class="iconfont-sys permission-node-icon"
                  :class="`${node.icon}`"
                  :style="{
                    fontSize: '16px',
                    color: '#666',
                  }"
                ></view>
                <text class="permission-node-title">{{ node.menu_name }}</text>
                <text class="permission-node-badge" :class="getPermissionTypeClass(node.menu_type)">
                  {{ getPermissionTypeText(node.menu_type) }}
                </text>
                <text class="permission-node-perms" v-if="node.perms && !$slots.actions">
                  {{ node.perms }}
                </text>
                <!-- 操作按钮插槽 -->
                <slot name="actions" :node="node" :level="1" v-if="!readonly"></slot>
              </view>
              <!-- 子级标识图标 -->
              <view
                class="permission-node-indicator"
                v-if="node.children && node.children.length > 0"
              >
                <wd-icon
                  :name="expandedPermissions.includes(node.id) ? 'arrow-down' : 'arrow-right'"
                  size="12"
                  custom-class="children-toggle-icon"
                />
              </view>
            </view>
          </template>

          <!-- 子权限 -->
          <view v-if="node.children && node.children.length > 0" class="permission-children">
            <wd-collapse v-model="expandedPermissions">
              <wd-collapse-item
                v-for="child in node.children"
                :key="child.id"
                :name="child.id"
                custom-body-style="padding: 0;"
                :custom-class="'permission-node-lv1'"
              >
                <template #title>
                  <view class="permission-node">
                    <view class="permission-node-info">
                      <view class="checkbox-container" @click.stop.prevent v-if="!readonly">
                        <wd-checkbox
                          :model-value="checkedPermissionIds.includes(child.id)"
                          :indeterminate="getIndeterminateState(child)"
                          @change="(checked) => handlePermissionCheck(child.id, checked)"
                          @click.stop.prevent
                          custom-class="permission-checkbox"
                        />
                      </view>
                      <!-- 使用wot组件图标 - 没有特殊前缀的图标 -->
                      <wd-icon
                        v-if="child.icon && !child.icon.startsWith('iconsys')"
                        :name="child.icon"
                        size="14"
                        custom-class="permission-node-icon"
                      />
                      <!-- 使用自定义图标 - iconsys开头的图标 -->
                      <view
                        v-else-if="child.icon && child.icon.startsWith('iconsys')"
                        class="iconfont-sys permission-node-icon"
                        :class="`${child.icon}`"
                        :style="{
                          fontSize: '14px',
                          color: '#666',
                        }"
                      ></view>
                      <text class="permission-node-title">{{ child.menu_name }}</text>
                      <text
                        class="permission-node-badge"
                        :class="getPermissionTypeClass(child.menu_type)"
                      >
                        {{ getPermissionTypeText(child.menu_type) }}
                      </text>
                      <text class="permission-node-perms" v-if="child.perms && !$slots.actions">
                        {{ child.perms }}
                      </text>
                      <!-- 操作按钮插槽 -->
                      <slot name="actions" :node="child" :level="2" v-if="!readonly"></slot>
                    </view>
                    <!-- 子级标识图标 -->
                    <view
                      class="permission-node-indicator"
                      v-if="child.children && child.children.length > 0"
                    >
                      <wd-icon
                        :name="
                          expandedPermissions.includes(child.id) ? 'arrow-down' : 'arrow-right'
                        "
                        size="12"
                        custom-class="children-toggle-icon"
                      />
                    </view>
                  </view>
                </template>

                <!-- 第三层子权限 -->
                <view
                  v-if="child.children && child.children.length > 0"
                  class="permission-children"
                >
                  <view
                    v-for="grandChild in child.children"
                    :key="grandChild.id"
                    class="permission-node permission-node-lv2"
                  >
                    <view class="permission-node-info">
                      <view class="checkbox-container" @click.stop.prevent v-if="!readonly">
                        <wd-checkbox
                          :model-value="checkedPermissionIds.includes(grandChild.id)"
                          @change="(checked) => handlePermissionCheck(grandChild.id, checked)"
                          @click.stop.prevent
                          custom-class="permission-checkbox"
                        />
                      </view>
                      <!-- 使用wot组件图标 - 没有特殊前缀的图标 -->
                      <wd-icon
                        v-if="grandChild.icon && !grandChild.icon.startsWith('iconsys')"
                        :name="grandChild.icon"
                        size="12"
                        custom-class="permission-node-icon"
                      />
                      <!-- 使用自定义图标 - iconsys开头的图标 -->
                      <view
                        v-else-if="grandChild.icon && grandChild.icon.startsWith('iconsys')"
                        class="iconfont-sys permission-node-icon"
                        :class="`${grandChild.icon}`"
                        :style="{
                          fontSize: '12px',
                          color: '#666',
                        }"
                      ></view>
                      <text class="permission-node-title">
                        {{ grandChild.menu_name }}
                      </text>
                      <text
                        class="permission-node-badge"
                        :class="getPermissionTypeClass(grandChild.menu_type)"
                      >
                        {{ getPermissionTypeText(grandChild.menu_type) }}
                      </text>
                      <text
                        class="permission-node-perms"
                        v-if="grandChild.perms && !$slots.actions"
                      >
                        {{ grandChild.perms }}
                      </text>
                      <!-- 操作按钮插槽 -->
                      <slot name="actions" :node="grandChild" :level="3" v-if="!readonly"></slot>
                    </view>
                    <!-- 子级标识图标 -->
                    <view
                      class="permission-node-indicator"
                      v-if="grandChild.children && grandChild.children.length > 0"
                    >
                      <wd-icon
                        :name="
                          expandedPermissions.includes(grandChild.id) ? 'arrow-down' : 'arrow-right'
                        "
                        size="12"
                        custom-class="children-toggle-icon"
                      />
                    </view>
                  </view>
                </view>
              </wd-collapse-item>
            </wd-collapse>
          </view>
        </wd-collapse-item>
      </wd-collapse>
    </view>

    <view v-else class="permission-empty">
      <wd-status-tip type="search" tip="暂无权限数据" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// 权限节点类型定义
export interface IPermissionTreeNode {
  id: string
  menu_name: string
  perms?: string
  menu_type?: number | null
  parent_id?: string | null
  icon?: string
  children?: IPermissionTreeNode[]
}

// 组件 Props
interface Props {
  permissionTree: IPermissionTreeNode[]
  modelValue: string[]
  readonly?: boolean
}

// 组件 Emits
interface Emits {
  (e: 'update:modelValue', value: string[]): void
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
})

const emit = defineEmits<Emits>()

// 内部状态
const checkedPermissionIds = ref<string[]>([])
const expandedPermissions = ref<string[]>([])
const cascadeSelection = ref(!props.readonly)

// 监听外部传入的选中状态
watch(
  () => props.modelValue,
  (newVal) => {
    // 避免重复设置相同的值，防止循环更新
    if (JSON.stringify(checkedPermissionIds.value) !== JSON.stringify(newVal)) {
      checkedPermissionIds.value = [...newVal]
    }
  },
  { immediate: true },
)

// 监听内部选中状态变化，向外发射事件
watch(
  () => checkedPermissionIds.value,
  (newVal) => {
    // 避免重复发射相同的值，防止循环更新
    if (JSON.stringify(props.modelValue) !== JSON.stringify(newVal)) {
      emit('update:modelValue', [...newVal])
    }
  },
  { deep: true },
)

// 权限类型相关函数
const getPermissionTypeText = (type?: number | null) => {
  switch (type) {
    case 1:
      return '目录'
    case 2:
      return '菜单'
    case 3:
      return '按钮'
    default:
      return '未知'
  }
}

const getPermissionTypeClass = (type?: number | null) => {
  switch (type) {
    case 1:
      return 'type-directory'
    case 2:
      return 'type-menu'
    case 3:
      return 'type-button'
    default:
      return ''
  }
}

// 获取节点的半选状态
const getIndeterminateState = (node: IPermissionTreeNode) => {
  if (!node.children || node.children.length === 0) return false

  // 如果节点本身已经被选中，不显示半选状态
  if (checkedPermissionIds.value.includes(node.id)) return false

  // 检查子节点的选中情况
  const checkedChildren = node.children.filter((child) =>
    checkedPermissionIds.value.includes(child.id),
  )

  // 如果部分子节点被选中，显示半选状态
  return checkedChildren.length > 0 && checkedChildren.length < node.children.length
}

// 权限树操作方法
const expandAll = () => {
  // 展开所有节点
  const allIds: string[] = []
  const collectIds = (nodes: IPermissionTreeNode[]) => {
    nodes.forEach((node) => {
      allIds.push(node.id)
      if (node.children && node.children.length > 0) {
        collectIds(node.children)
      }
    })
  }
  collectIds(props.permissionTree)
  expandedPermissions.value = allIds
}

const collapseAll = () => {
  // 收起所有节点
  expandedPermissions.value = []
}

const checkAll = () => {
  // 全选所有权限
  const allIds: string[] = []
  const collectIds = (nodes: IPermissionTreeNode[]) => {
    nodes.forEach((node) => {
      allIds.push(node.id)
      if (node.children && node.children.length > 0) {
        collectIds(node.children)
      }
    })
  }
  collectIds(props.permissionTree)
  checkedPermissionIds.value = allIds
}

const uncheckAll = () => {
  // 取消全选
  checkedPermissionIds.value = []
}

// 递归获取所有子节点ID
const getAllChildrenIds = (node: IPermissionTreeNode): string[] => {
  const ids: string[] = []
  if (node.children && node.children.length > 0) {
    node.children.forEach((child) => {
      ids.push(child.id)
      ids.push(...getAllChildrenIds(child))
    })
  }
  return ids
}

// 查找父节点
const findParentNode = (
  targetId: string,
  nodes: IPermissionTreeNode[],
  parentId?: string,
): string | null => {
  for (const node of nodes) {
    if (node.children && node.children.some((child) => child.id === targetId)) {
      return node.id
    }
    if (node.children && node.children.length > 0) {
      const found = findParentNode(targetId, node.children, node.id)
      if (found) return found
    }
  }
  return null
}

// 处理权限选择
const handlePermissionCheck = (permissionId: string, checked: boolean | { value: boolean }) => {
  // 在只读模式下不允许选择
  if (props.readonly) return

  // 处理checked参数，可能是布尔值或对象
  const isChecked = typeof checked === 'boolean' ? checked : checked.value

  console.log('权限选择变化:', permissionId, 'checked:', isChecked)

  // 查找当前节点
  const findNode = (id: string, nodes: IPermissionTreeNode[]): IPermissionTreeNode | null => {
    for (const node of nodes) {
      if (node.id === id) return node
      if (node.children && node.children.length > 0) {
        const found = findNode(id, node.children)
        if (found) return found
      }
    }
    return null
  }

  const currentNode = findNode(permissionId, props.permissionTree)
  if (!currentNode) return

  // 创建一个新的选中数组副本
  let newCheckedIds = [...checkedPermissionIds.value]

  if (isChecked) {
    // 选中当前节点
    if (!newCheckedIds.includes(permissionId)) {
      newCheckedIds.push(permissionId)
    }

    // 如果开启子级联动，选中所有子节点
    if (cascadeSelection.value) {
      const childrenIds = getAllChildrenIds(currentNode)
      childrenIds.forEach((childId) => {
        if (!newCheckedIds.includes(childId)) {
          newCheckedIds.push(childId)
        }
      })

      // 向上检查父节点（非递归）
      let currentCheckId = permissionId
      while (true) {
        const parentId = findParentNode(currentCheckId, props.permissionTree)
        if (!parentId) break

        // 检查是否所有兄弟节点都被选中
        const parentNode = findNode(parentId, props.permissionTree)
        if (parentNode && parentNode.children) {
          const allSiblingsChecked = parentNode.children.every((child) =>
            newCheckedIds.includes(child.id),
          )
          if (allSiblingsChecked && !newCheckedIds.includes(parentId)) {
            newCheckedIds.push(parentId)
            currentCheckId = parentId // 继续向上检查
          } else {
            break // 不满足条件，停止向上检查
          }
        } else {
          break
        }
      }
    }
  } else {
    // 取消当前节点
    const index = newCheckedIds.indexOf(permissionId)
    if (index > -1) {
      newCheckedIds.splice(index, 1)
    }

    // 如果开启子级联动，取消所有子节点
    if (cascadeSelection.value) {
      const childrenIds = getAllChildrenIds(currentNode)
      childrenIds.forEach((childId) => {
        const childIndex = newCheckedIds.indexOf(childId)
        if (childIndex > -1) {
          newCheckedIds.splice(childIndex, 1)
        }
      })

      // 向上取消父节点（非递归）
      let currentUncheckId = permissionId
      while (true) {
        const parentId = findParentNode(currentUncheckId, props.permissionTree)
        if (!parentId) break

        const parentIndex = newCheckedIds.indexOf(parentId)
        if (parentIndex > -1) {
          newCheckedIds.splice(parentIndex, 1)
          currentUncheckId = parentId // 继续向上取消
        } else {
          break // 父节点本来就没被选中，停止
        }
      }
    }
  }

  // 一次性更新选中状态
  checkedPermissionIds.value = newCheckedIds
}
</script>

<style lang="scss" scoped>
.permission-container {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
}

.permission-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eee;
}

.permission-actions {
  display: flex;
  gap: 6px;
  align-items: center;
  width: 100%;
  justify-content: flex-start;
}

.permission-cascade-switch {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-right: 8px;
  padding-right: 8px;
  border-right: 1px solid #eee;
}

.switch-label {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.permission-tree {
  padding: 8px 0;
}

.permission-empty {
  padding: 30px 20px;
  text-align: center;
}

// 权限节点样式
.permission-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  width: 100%;
}

.permission-node-info {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
}

.permission-node-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-left: 8px;
  flex-shrink: 0;
}

:deep(.children-toggle-icon) {
  color: #4285f4 !important;
  transition: transform 0.2s ease;
}

.permission-checkbox {
  margin-right: 6px;
}

.checkbox-container {
  display: inline-flex;
  align-items: center;
  margin-right: 6px;
}

.permission-node-icon {
  color: #666;
}

.permission-node-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.permission-node-badge {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  white-space: nowrap;

  &.type-directory {
    background-color: #e3f2fd;
    color: #1976d2;
  }

  &.type-menu {
    background-color: #f3e5f5;
    color: #7b1fa2;
  }

  &.type-button {
    background-color: #e8f5e8;
    color: #388e3c;
  }
}

.permission-node-perms {
  font-size: 12px;
  color: #999;
  font-family: monospace;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 1;
  min-width: 0;
}

// 操作按钮插槽样式
:deep(.permission-actions) {
  display: flex;
  gap: 4px;
  align-items: center;
  margin-left: 8px;
  flex-shrink: 0;
}

.permission-children {
  padding-left: 16px;
  border-left: 2px solid #f0f0f0;
  margin-top: 4px;
}

// 不同层级的样式
:deep(.permission-node-lv0) {
  .wd-collapse-item__header {
    background-color: #fafafa;
    font-weight: 600;
  }
}

:deep(.permission-node-lv1) {
  .wd-collapse-item__header {
    background-color: #f5f5f5;
    font-weight: 500;
    margin-top: 2px;
  }
}

.permission-node-lv2 {
  padding: 8px 4px 8px 5px;
  background-color: #fbfbfb;
  border-bottom: 1px solid #f0f0f0;
  margin-top: 2px;

  .permission-node-title {
    font-size: 13px;
    font-weight: normal;
  }

  .permission-node-info {
    flex: 1;
    min-width: 0;
    overflow: hidden;
  }
}

// 折叠面板自定义样式
:deep(.wd-collapse-item) {
  margin-bottom: 4px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e8e8e8;
}

:deep(.wd-collapse-item__header) {
  padding: 10px 12px;
  background-color: #f8f9fa;
}

:deep(.wd-collapse-item__body) {
  background-color: white;
}
</style> 