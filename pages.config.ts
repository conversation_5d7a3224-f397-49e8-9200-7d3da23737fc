import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'
import { tabBar } from './src/components/tab-bar/tabbarList'

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: 'invoice-book-uniapp',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
    backgroundColor: '#FFFFFF',
  },
  easycom: {
    autoscan: true,
    custom: {
      '^fg-(.*)': '@/components/fg-$1/fg-$1.vue',
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
      SysTabBar: '@/pages-sys/components/sys-tab-bar/sys-tab-bar.vue',
    },
  },
  tabBar: tabBar as any,
  // 如果不需要原生tabBar，可以注释掉这个配置，使用wot组件
  // tabBar: {
  //   color: '#999999',
  //   selectedColor: '#018d71',
  //   backgroundColor: '#F8F8F8',
  //   borderStyle: 'black',
  //   height: '50px',
  //   fontSize: '10px',
  //   iconWidth: '24px',
  //   spacing: '3px',
  //   list: [
  //     // 普通用户页面
  //     {
  //       pagePath: 'pages/user/index',
  //     },
  //     {
  //       pagePath: 'pages/user/category',
  //     },
  //     {
  //       pagePath: 'pages/user/mine',
  //     },
  //     // 商户用户页面
  //     {
  //       pagePath: 'pages/merchants/index',
  //     },
  //     {
  //       pagePath: 'pages/merchants/statistics',
  //     },
  //     {
  //       pagePath: 'pages/merchants/orders',
  //     },
  //     {
  //       pagePath: 'pages/merchants/goods',
  //     },
  //     {
  //       pagePath: 'pages/merchants/mine',
  //     },
  //     // 系统管理员页面
  //     {
  //       pagePath: 'pages/sys/dashboard',
  //     },
  //     {
  //       pagePath: 'pages/sys/merchants',
  //     },
  //     {
  //       pagePath: 'pages/sys/goods',
  //     },
  //     {
  //       pagePath: 'pages/sys/system',
  //     },
  //     {
  //       pagePath: 'pages/sys/mine',
  //     },
  //   ],
  // },
  // 预下载分包 (可选)
  preloadRule: {
    'pages-merchants/login/login': {
      network: 'all',
      packages: ['pages-merchants'], // 商户登录时预下载商户管理分包
    },
    'pages-sys/login/login': {
      network: 'all',
      packages: ['pages-sys'], // 管理员登录时预下载系统管理分包
    },
  },
})
