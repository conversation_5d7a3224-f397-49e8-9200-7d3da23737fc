import { ref, type Ref } from 'vue'

/**
 * useUpload 是一个定制化的请求钩子，用于处理上传图片。
 * @param formData 额外传递给后台的数据，如{name: '菲鸽'}。
 * @returns 返回一个对象{loading, error, data, run}，包含请求的加载状态、错误信息、响应数据和手动触发请求的函数。
 */
export default function useUpload<T = string>(formData: Record<string, any> = {}) {
  const loading = ref(false)
  const error = ref(false)
  const data = ref<T>()

  /**
   * 执行上传操作
   * @param url 上传URL路径（如：'/sys-user/avatar'）
   * @param name 表单字段名（如：'avatar' 或 'file'）
   */
  const run = (url?: string, name?: string) => {
    // 如果没有传入 url 和 name，使用默认值
    // const uploadUrl = url ? VITE_UPLOAD_BASEURL + url : VITE_UPLOAD_BASEURL
    const uploadUrl = url
    const fieldName = name || 'file'

    // #ifdef MP-WEIXIN
    // 微信小程序从基础库 2.21.0 开始， wx.chooseImage 停止维护，请使用 uni.chooseMedia 代替。
    // 微信小程序在2023年10月17日之后，使用本API需要配置隐私协议
    uni.chooseMedia({
      count: 1,
      mediaType: ['image'],
      success: (res) => {
        loading.value = true
        error.value = false
        const tempFilePath = res.tempFiles[0].tempFilePath

        // 使用传入的配置进行上传
        uploadFileConfig<T>({ tempFilePath, formData, data, error, loading }, { url: uploadUrl, name: fieldName })
      },
      fail: (err) => {
        console.error('uni.chooseMedia err->', err)
        error.value = true
      },
    })
    // #endif
    // #ifndef MP-WEIXIN
    uni.chooseImage({
      count: 1,
      success: (res) => {
        loading.value = true
        error.value = false
        const tempFilePath = res.tempFilePaths[0]

        // 使用传入的配置进行上传
        uploadFileConfig<T>({ tempFilePath, formData, data, error, loading }, { url: uploadUrl, name: fieldName })
      },
      fail: (err) => {
        console.error('uni.chooseImage err->', err)
        error.value = true
      },
    })
    // #endif
  }

  return { loading, error, data, run }
}

/**
 * 配置化上传函数实现
 */
function uploadFileConfig<T>({ tempFilePath, formData, data, error, loading }, { url, name }) {
  uni.uploadFile({
    url: url,
    filePath: tempFilePath,
    name: name,
    formData,
    success: (uploadFileRes) => {
      data.value = uploadFileRes.data as T
    },
    fail: (err) => {
      console.error('uni.uploadFile err->', err)
      error.value = true
    },
    complete: () => {
      loading.value = false
    },
  })
}
