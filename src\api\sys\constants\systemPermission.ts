/**
 * 菜单类型枚举
 */
export enum MenuType {
  // 目录
  DIRECTORY = 1,
  // 菜单
  MENU = 2,
  // 按钮
  BUTTON = 3
}

/**
 * 菜单类型映射
 */
export const MenuTypeMap = {
  [MenuType.DIRECTORY]: '目录',
  [MenuType.MENU]: '菜单',
  [MenuType.BUTTON]: '按钮'
}

/**
 * 菜单状态枚举
 */
export enum MenuStatus {
  // 正常
  NORMAL = 0,
  // 停用
  DISABLED = 1
}

/**
 * 菜单状态映射
 */
export const MenuStatusMap = {
  [MenuStatus.NORMAL]: '正常',
  [MenuStatus.DISABLED]: '停用'
}

/**
 * 菜单状态颜色映射
 */
export const MenuStatusColorMap = {
  [MenuStatus.NORMAL]: 'success',
  [MenuStatus.DISABLED]: 'danger'
}

/**
 * 显示状态枚举
 */
export enum VisibleStatus {
  // 显示
  SHOW = 0,
  // 隐藏
  HIDE = 1
}

/**
 * 显示状态映射
 */
export const VisibleStatusMap = {
  [VisibleStatus.SHOW]: '显示',
  [VisibleStatus.HIDE]: '隐藏'
}

/**
 * 是否外链枚举
 */
export enum FrameStatus {
  // 是外链
  YES = 0,
  // 否
  NO = 1
}

/**
 * 是否外链映射
 */
export const FrameStatusMap = {
  [FrameStatus.YES]: '是',
  [FrameStatus.NO]: '否'
}

/**
 * 是否缓存枚举
 */
export enum CacheStatus {
  // 缓存
  CACHE = 0,
  // 不缓存
  NO_CACHE = 1
}

/**
 * 是否缓存映射
 */
export const CacheStatusMap = {
  [CacheStatus.CACHE]: '缓存',
  [CacheStatus.NO_CACHE]: '不缓存'
} 