<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '微信信息',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="wechat-info-container">
    <!-- 导航栏 -->
    <Navbar
      title="微信信息"
      :fixed="true"
      :placeholder="true"
      custom-class="custom-navbar"
      @back="handleBack"
    />

    <!-- 内容区域 -->
    <view class="page-content">
      <view class="main-layout">
        <!-- 微信信息填写区域 -->
        <view class="wechat-info-section">
          <!-- 头像选择 -->
          <view class="avatar-area">
            <wd-button
              class="avatar-btn"
              open-type="chooseAvatar"
              @chooseavatar="onChooseAvatar"
              custom-style="background: transparent; border: none; padding: 0; width: 100px; height: 100px;"
            >
              <view class="avatar-container">
                <wd-img
                  v-if="selectedAvatar"
                  :src="selectedAvatar"
                  class="avatar-image"
                  mode="aspectFill"
                  width="100px"
                  height="110px"
                  round
                />
                <view v-else class="avatar-placeholder">
                  <text class="iconfont-sys iconsys-camera"></text>
                </view>

                <!-- 上传状态覆盖层 -->
                <view v-if="isUploadingAvatar" class="upload-overlay">
                  <view class="upload-loading">
                    <text class="loading-text">上传中...</text>
                  </view>
                </view>
              </view>
            </wd-button>

            <!-- 头像状态提示 -->
            <view class="avatar-status">
              <text v-if="isUploadingAvatar" class="status-uploading">正在上传头像...</text>
              <text v-else-if="serverAvatarUrl" class="status-success">头像上传成功</text>
              <text v-else class="status-hint">点击选择头像</text>
            </view>
          </view>

          <!-- 昵称输入 -->
          <view class="nickname-area">
            <view class="nickname-label">昵称</view>
            <view class="nickname-input-wrapper">
              <wd-input
                v-model="nickname"
                type="nickname"
                placeholder="请输入昵称"
                :maxlength="20"
                custom-class="nickname-input"
                @input="onNicknameInput"
              />
              <view class="input-counter">{{ nickname.length }}/20</view>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-section">
          <wd-button
            size="large"
            custom-class="cancel-btn"
            custom-style="flex: 1; height: 48px; border-radius: 24px; background: white; color: #666; border: 1px solid #e0e0e0; margin-right: 12px;"
            @click="handleCancel"
          >
            取消
          </wd-button>
          <wd-button
            type="primary"
            size="large"
            :disabled="!canConfirm || isBinding"
            custom-class="confirm-btn"
            custom-style="flex: 1; height: 48px; border-radius: 24px;"
            @click="handleConfirm"
          >
            {{ isBinding ? '绑定中...' : '确认绑定' }}
          </wd-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import WxUtil from '@/utils/wxUtil'
import { bindWechatApi, type IWechatBindForm } from '@/api/sys/systemAuthApi'

// 页面参数
const action = ref<'bind' | 'update'>('bind')

// 状态管理
const selectedAvatar = ref('')
const nickname = ref('')
const isBinding = ref(false)
const isUploadingAvatar = ref(false)
const serverAvatarUrl = ref('') // 服务器返回的头像URL

// 计算属性
const canConfirm = computed(() => {
  return serverAvatarUrl.value && nickname.value.trim().length > 0 && !isUploadingAvatar.value
})

// 页面加载
onLoad((options) => {
  if (options?.action) {
    action.value = options.action as 'bind' | 'update'
  }
})

// 选择头像并上传
const onChooseAvatar = (e: any) => {
  console.log('头像选择事件:', e)
  const avatarUrl = e.detail?.avatarUrl || e.detail?.avatar || e.avatarUrl
  if (avatarUrl) {
    console.log('用户选择头像:', avatarUrl)
    // 立即显示选择的头像
    selectedAvatar.value = avatarUrl
    // 开始上传到服务器
    uploadAvatarToServer(avatarUrl)
  } else {
    console.error('未能获取到头像URL:', e)
    uni.showToast({
      title: '头像选择失败',
      icon: 'none',
    })
  }
}

// 上传头像到服务器
const uploadAvatarToServer = (tempFilePath: string) => {
  console.log('开始上传头像到服务器:', tempFilePath)
  isUploadingAvatar.value = true

  uni.uploadFile({
    url: '/sys-user/avatar',
    filePath: tempFilePath,
    name: 'avatar',
    success: (uploadFileRes) => {
      try {
        console.log('头像上传响应:', uploadFileRes.data)

        // 解析上传返回的数据
        const uploadResult = JSON.parse(uploadFileRes.data)

        // 检查返回结果的格式
        if (uploadResult.code === 200 && uploadResult.data && uploadResult.data.file_url) {
          serverAvatarUrl.value = uploadResult.data.file_url
          console.log('头像上传成功，服务器URL:', serverAvatarUrl.value)

          uni.showToast({
            title: '头像上传成功',
            icon: 'success',
          })
        } else {
          const errorMsg = uploadResult.message || '上传失败'
          console.error('头像上传失败:', errorMsg)
          uni.showToast({
            title: errorMsg,
            icon: 'none',
          })
          // 清空头像显示
          selectedAvatar.value = ''
          serverAvatarUrl.value = ''
        }
      } catch (parseError) {
        console.error('解析头像上传结果失败:', parseError)
        uni.showToast({
          title: '上传结果解析失败',
          icon: 'none',
        })
        // 清空头像显示
        selectedAvatar.value = ''
        serverAvatarUrl.value = ''
      }
    },
    fail: (err) => {
      console.error('头像上传请求失败:', err)
      uni.showToast({
        title: '头像上传失败',
        icon: 'none',
      })
      // 清空头像显示
      selectedAvatar.value = ''
      serverAvatarUrl.value = ''
    },
    complete: () => {
      isUploadingAvatar.value = false
    },
  })
}

// 昵称输入
const onNicknameInput = (e: any) => {
  console.log('昵称输入事件:', e)
  if (e && e.detail && typeof e.detail.value !== 'undefined') {
    nickname.value = e.detail.value
  } else if (e && typeof e === 'string') {
    // 处理直接传入字符串的情况
    nickname.value = e
  } else {
    console.warn('昵称输入事件格式异常:', e)
  }
}

// 确认操作
const handleConfirm = async () => {
  if (!canConfirm.value) {
    uni.showToast({
      title: '请完善信息',
      icon: 'none',
    })
    return
  }

  if (isBinding.value) {
    return // 防止重复提交
  }

  isBinding.value = true

  try {
    // 在确认绑定时获取微信授权码，保证code的时效性
    console.log('开始获取微信授权码')
    const wxLoginResult = await WxUtil.login()
    console.log('获取到微信登录code:', wxLoginResult.code)

    // 直接在这里调用绑定API，使用服务器返回的头像URL
    const bindForm: IWechatBindForm = {
      code: wxLoginResult.code,
      nickname: WxUtil.formatNickname(nickname.value),
      avatar: serverAvatarUrl.value, // 使用服务器返回的头像URL
    }

    console.log('开始调用微信绑定API:', bindForm)
    const result = await bindWechatApi(bindForm)

    if (result.code === 200) {
      uni.showToast({
        title: '微信绑定成功',
        icon: 'success',
      })

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        // 返回到个人信息页面
        uni.navigateBack({
          fail: () => {
            // 如果返回失败，直接跳转到个人信息页面
            uni.redirectTo({
              url: '/pages-sys/profile/index',
            })
          },
        })
      }, 1500)
    } else {
      uni.showToast({
        title: result.message || '微信绑定失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('微信绑定失败:', error)
    uni.showToast({
      title: '微信绑定失败',
      icon: 'none',
    })
  } finally {
    isBinding.value = false
  }
}

// 取消操作 - 返回到个人信息页面
const handleCancel = () => {
  uni.navigateBack({
    fail: () => {
      // 如果返回失败，直接跳转到个人信息页面
      uni.redirectTo({
        url: '/pages-sys/profile/index',
      })
    },
  })
}

// 返回处理 - 返回到个人信息页面
const handleBack = () => {
  console.log('点击返回按钮')
  uni.navigateBack({
    fail: () => {
      // 如果返回失败，直接跳转到个人信息页面
      uni.redirectTo({
        url: '/pages-sys/profile/index',
      })
    },
  })
}
</script>

<style lang="scss" scoped>
.wechat-info-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: white;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #07c160 !important;

  .wd-navbar__title {
    color: white !important;
    font-size: 20px !important;
    font-weight: bold !important;
  }

  .wd-navbar__left .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;

    .wd-icon {
      font-size: 26px !important;
      color: white !important;
    }
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.main-layout {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

// 微信信息填写区域
.wechat-info-section {
  padding: 40px 24px;
  display: flex;
  flex-direction: column;
  gap: 40px;
}

// 头像区域
.avatar-area {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.avatar-container {
  width: 100px;
  height: 100px;
  border: 2px dashed #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: transparent;
}

// 头像按钮样式
:deep(.avatar-btn) {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
}

// 昵称区域
.nickname-area {
  display: flex;
  align-items: center;
  gap: 16px;
}

.nickname-label {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  min-width: 40px;
}

:deep(.avatar-btn) {
  overflow: hidden;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}

// 头像图片样式
:deep(.avatar-image) {
  width: 100px !important;
  height: 110px !important;
  border-radius: 50% !important;
  overflow: hidden !important;

  // 小程序端兼容
  .wd-img__image {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    border-radius: 50% !important;
  }
}

.avatar-placeholder {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;

  .iconfont-sys {
    font-size: 32px;
    color: #999;
  }
}

// 上传状态覆盖层
.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.loading-text {
  color: white;
  font-size: 12px;
}

// 头像状态提示
.avatar-status {
  margin-top: 12px;
  text-align: center;
}

.status-uploading {
  color: #07c160;
  font-size: 12px;
}

.status-success {
  color: #07c160;
  font-size: 12px;
}

.status-hint {
  color: #999;
  font-size: 12px;
}

.nickname-input-wrapper {
  position: relative;
  flex: 1;
}

:deep(.nickname-input) {
  .wd-input {
    width: 100%;
    height: 48px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 0 16px;
    font-size: 16px;
    background: white;

    &:focus {
      border-color: #07c160;
      outline: none;
    }
  }
}

.input-counter {
  position: absolute;
  right: 12px;
  bottom: -24px;
  font-size: 12px;
  color: #999;
}

// 操作按钮区域
.action-section {
  display: flex;
  flex-direction: row;
  gap: 0;
  margin-top: 20px;
}
</style>
