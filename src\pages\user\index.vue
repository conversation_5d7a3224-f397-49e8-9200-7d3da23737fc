<route lang="json5" type="home">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '主页',
  },
  access: {
    requireAuth: false,
  },
}
</route>
<template>
  <view class="home-container">
    <!-- 页头和搜索栏统一容器 -->
    <view class="header-container" :style="{ paddingTop: safeAreaInsetsTop + 'px' }">
      <!-- 页头 -->
      <view class="page-header" :style="{ paddingRight: rightSafeArea + 'px' }">
        <LocationHeader @click="handleLocationClick" />
      </view>

      <!-- 搜索栏 -->
      <view class="search-header">
        <view
          class="search-container"
          :class="{
            'recommend-mode': activeFilter === 'recommend',
            'other-mode': activeFilter !== 'recommend',
          }"
          @click="goToSearch"
        >
          <view class="search-input-fake">
            <text class="search-placeholder">搜索商品、店铺</text>
          </view>
          <view class="search-button">
            <text class="search-text">搜索</text>
          </view>
        </view>
        <!-- 显示模式切换按钮 - 只在非推荐标签页显示 -->
        <view
          v-if="activeFilter !== 'recommend'"
          class="header-view-toggle-button show"
          @click="toggleViewMode"
        >
          <text
            class="iconfont-sys"
            :class="viewMode === 'grid' ? 'iconsys-liebiao' : 'iconsys-more-grid-big'"
          ></text>
        </view>
      </view>
    </view>
    <!-- 分类筛选栏 -->
    <view class="filter-bar">
      <wd-tabs
        v-model="activeFilter"
        @change="handleFilterChange"
        line-width="30px"
        line-height="3px"
        auto-line-width
        swipeable
        animated
        slidable="always"
      >
        <wd-tab
          v-for="filter in filterTabs"
          :key="filter.value"
          :name="filter.value"
          :title="filter.label"
        >
          <!-- 推荐页面使用swiper -->
          <view v-if="filter.value === 'recommend'" class="swiper-container">
            <swiper
              class="goods-swiper"
              vertical
              :current="currentGoodsIndex"
              :autoplay="false"
              :indicator-dots="false"
              :style="{ height: swiperHeight }"
              @change="handleGoodsChange"
              :circular="false"
            >
              <swiper-item
                v-for="(item, index) in goodsList"
                :key="item.id"
                class="swiper-item-container"
              >
                <view class="goods-card" @click="handleGoodsClick(index, item)">
                  <view class="goods-image-wrapper">
                    <!-- 商品图片 -->
                    <image class="goods-image" :src="item.images[0]" mode="aspectFill"></image>

                    <!-- 商品信息卡片 -->
                    <GoodsInfoCard
                      :price="item.price"
                      :sales="item.sales"
                      :title="item.title"
                      :specs="item.specs"
                      :description="item.description"
                    />
                  </view>

                  <!-- 商品详细信息区域 -->
                  <view class="goods-details">
                    <MerchantInfoCard
                      :merchant-logo="item.merchantLogo"
                      :shop-name="item.shopName"
                      :merchant-id="item.id"
                      @click="goToMerchantHome(item)"
                    />
                  </view>
                </view>
              </swiper-item>

              <!-- 加载状态 -->
              <swiper-item
                v-if="isLoading && goodsList.length === 0"
                class="swiper-item-container loading-item"
              >
                <LoadingSpinner />
              </swiper-item>
            </swiper>
          </view>

          <!-- 其他分类使用瀑布流 -->
          <view v-else class="category-container">
            <scroll-view
              class="waterfall-scroll-container"
              scroll-y
              @scrolltolower="handleWaterfallReachBottom"
              :lower-threshold="200"
            >
              <view class="waterfall-content">
                <!-- 网格模式 -->
                <WaterfallGoods
                  v-if="viewMode === 'grid'"
                  :goods-list="waterfallGoodsList"
                  :loading="waterfallLoading"
                  :has-more="waterfallHasMore"
                  :column-count="2"
                  :show-price="false"
                  loading-text="加载中..."
                  no-more-text="没有更多商品了"
                  @goods-click="handleWaterfallGoodsClick"
                  @like-click="handleWaterfallLikeClick"
                />

                <!-- 列表模式 -->
                <GoodsList
                  v-else-if="viewMode === 'list'"
                  :goods-list="convertToListGoods(waterfallGoodsList)"
                  :loading="waterfallLoading"
                  :has-more="waterfallHasMore"
                  :show-price="true"
                  loading-text="加载中..."
                  no-more-text="没有更多商品了"
                  @goods-click="handleListGoodsClick"
                  @like-click="handleListLikeClick"
                />
              </view>
            </scroll-view>
          </view>
        </wd-tab>
      </wd-tabs>
    </view>

    <!-- 底部导航栏 -->
    <TabBar />
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useGlobalSafeArea } from '@/hooks/useSafeArea'
import type { GoodsItem as WaterfallGoodsItem } from '@/components/waterfall-goods/types'
// import { TestTabBar } from '@/components/tab-bar/test.vue'

defineOptions({
  name: 'UserIndex',
})

// 使用全局安全区域 hook
const { safeAreaInsetsTop, rightSafeArea } = useGlobalSafeArea()

// 筛选相关
const activeFilter = ref('recommend')
const filterTabs = [
  { label: '推荐', value: 'recommend' },
  { label: '蔬菜', value: 'vegetables' },
  { label: '水果', value: 'fruits' },
  { label: '肉类', value: 'meat' },
  { label: '海鲜', value: 'seafood' },
  { label: '粮油', value: 'grains' },
  { label: '零食', value: 'snacks' },
  { label: '饮品', value: 'drinks' },
]

// 显示模式
const viewMode = ref<'list' | 'grid'>('grid') // 显示模式：'list' 列表视图, 'grid' 网格视图

// 当前商品索引
const currentGoodsIndex = ref(0)

// 推荐页面索引记录（用于保持用户浏览位置）
const recommendGoodsIndex = ref(0)

// 无限滚动相关状态
const isLoading = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)
const pageSize = 8 // 每页加载数据量

// swiper高度
const swiperHeight = ref('65vh')

// 当前显示的商品列表
const goodsList = ref([])

// 瀑布流商品数据（用于销量和新上架标签页）
const waterfallGoodsList = ref<WaterfallGoodsItem[]>([])
const waterfallLoading = ref(false)
const waterfallHasMore = ref(true)
const waterfallCurrentPage = ref(1)

// 动态生成模拟商品数据
const generateMockGoods = (category: string, startIndex: number, count: number) => {
  const titles = [
    '新鲜有机西红柿 自然成熟 酸甜可口',
    '精选苹果 脆甜多汁 营养丰富',
    '新鲜黄瓜 翠绿爽脆 无农药残留',
    '优质香蕉 香甜软糯 进口品质',
    '有机胡萝卜 营养丰富 口感清甜',
    '新鲜草莓 酸甜可口 维C丰富',
    '精选土豆 粉糯香甜 农家直供',
    '新鲜橙子 汁多味甜 维生素丰富',
    '有机白菜 嫩绿新鲜 无污染种植',
    '优质猪肉 新鲜切割 肉质鲜美',
    '深海带鱼 肉质鲜嫩 营养价值高',
    '精选大米 颗粒饱满 香味浓郁',
    '纯正花生油 压榨工艺 健康营养',
    '休闲零食大礼包 多种口味 老少皆宜',
    '纯净矿泉水 天然水源 口感甘甜',
  ]

  const shops = [
    '新鲜果蔬专营店',
    '有机农场直供',
    '绿色蔬菜基地',
    '优质水果商行',
    '农家直销店',
    '生鲜超市',
    '健康食品专营',
    '进口水果店',
    '有机食品专卖',
    '新鲜肉类专营',
    '海鲜水产直销',
    '优质粮油店',
    '休闲零食铺',
    '天然饮品店',
    '绿色食品超市',
  ]

  // 定义纯色背景颜色数组
  const colors = [
    '#FF6B6B',
    '#4ECDC4',
    '#45B7D1',
    '#96CEB4',
    '#FFEAA7',
    '#DDA0DD',
    '#98D8C8',
    '#F7DC6F',
    '#BB8FCE',
    '#85C1E9',
    '#F8C471',
    '#82E0AA',
    '#F1948A',
    '#85C1E9',
    '#D7BDE2',
    '#A3E4D7',
    '#F9E79F',
    '#D5A6BD',
    '#AED6F1',
    '#A9DFBF',
  ]

  // 商户头像图片数组
  const merchantLogos = [
    'https://images.unsplash.com/photo-1472851294608-062f824d29cc?w=100&h=100&fit=crop', // 商店门面
    'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=100&h=100&fit=crop', // 购物袋
    'https://images.unsplash.com/photo-1534723452862-4c874018d66d?w=100&h=100&fit=crop', // 水果店
    'https://images.unsplash.com/photo-1542838132-92c53300491e?w=100&h=100&fit=crop', // 杂货店
    'https://images.unsplash.com/photo-1604719312566-8912e9227c6a?w=100&h=100&fit=crop', // 超市
    'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=100&h=100&fit=crop', // 农贸市场
    'https://images.unsplash.com/photo-1488459716781-31db52582fe9?w=100&h=100&fit=crop', // 有机食品店
    'https://images.unsplash.com/photo-1568745689564-64ba2e3f2439?w=100&h=100&fit=crop', // 新鲜市场
    'https://images.unsplash.com/photo-1604934842861-4a0b58f00b70?w=100&h=100&fit=crop', // 食品店
    'https://images.unsplash.com/photo-1594995846645-d58328c3ffa4?w=100&h=100&fit=crop', // 生鲜超市
  ]

  // 生成每个商品使用一张图片
  const imageUrls = [
    'https://images.unsplash.com/photo-1619546813926-a78fa6372cd2?w=800&h=600&fit=crop', // 保留第一张
    'https://images.unsplash.com/photo-1567306226416-28f0efdc88ce?w=800&h=600&fit=crop', // 苹果
    'https://images.unsplash.com/photo-1603833665858-e61d17a86224?w=800&h=600&fit=crop', // 香蕉
    'https://images.unsplash.com/photo-1519996529931-28324d5a630e?w=800&h=600&fit=crop', // 水果
    'https://images.unsplash.com/photo-1550258987-190a2d41a8ba?w=800&h=600&fit=crop', // 菠萝
    'https://images.unsplash.com/photo-1587132137056-bfbf0166836e?w=800&h=600&fit=crop', // 橙子
    'https://images.unsplash.com/photo-1573246123716-6b1782bfc499?w=800&h=600&fit=crop', // 草莓
    'https://images.unsplash.com/photo-1546548970-71785318a17b?w=800&h=600&fit=crop', // 蔬菜
    'https://images.unsplash.com/photo-1540420773420-3366772f4999?w=800&h=600&fit=crop', // 沙拉
    'https://images.unsplash.com/photo-1498837167922-ddd27525d352?w=800&h=600&fit=crop', // 蔬菜拼盘
  ]

  return Array.from({ length: count }, (_, i) => {
    const index = startIndex + i
    const titleIndex = index % titles.length
    const shopIndex = index % shops.length
    const colorIndex = index % colors.length
    const imageIndex = index % imageUrls.length
    const merchantLogoIndex = index % merchantLogos.length
    const title = `${titles[titleIndex]}`

    // 生成规格
    const specs = []

    // 根据商品类型添加不同规格
    if (title.includes('西红柿') || title.includes('黄瓜') || title.includes('胡萝卜')) {
      specs.push('500g装')
      specs.push('有机认证')
    } else if (title.includes('苹果') || title.includes('香蕉') || title.includes('橙子')) {
      specs.push('1kg装')
      specs.push('进口品质')
    } else if (title.includes('猪肉') || title.includes('带鱼')) {
      specs.push('新鲜切割')
      specs.push('冷链配送')
    } else if (title.includes('大米') || title.includes('花生油')) {
      specs.push('5kg装')
      specs.push('品质保证')
    } else {
      specs.push('精装')
      specs.push('新鲜直供')
    }

    // 生成描述
    const descriptions = [
      '新鲜采摘，当日配送，保证品质和口感。',
      '严格筛选，品质优良，营养丰富健康。',
      '农场直供，绿色无污染，安全放心。',
      '精心挑选，口感佳，营养价值高。',
      '天然种植，无添加剂，健康美味。',
    ]
    let description = descriptions[index % descriptions.length]

    // 根据商品类型添加特定描述
    if (title.includes('有机')) {
      description += ' 通过有机认证，种植过程无化学农药。'
    } else if (title.includes('进口')) {
      description += ' 进口优质品种，品质保证。'
    } else if (title.includes('新鲜')) {
      description += ' 当日采摘配送，保证新鲜度。'
    }

    return {
      id: `${category}_${index}`,
      title: title,
      images: [imageUrls[imageIndex]],
      price: (Math.random() * 50 + 5).toFixed(2), // 食品价格相对较低
      sales: Math.floor(Math.random() * 5000 + 100),
      shopName: shops[shopIndex],
      merchantLogo: merchantLogos[merchantLogoIndex], // 使用实际图片URL
      merchantLogoColor: colors[(shopIndex + 5) % colors.length], // 保留颜色作为备用
      specs: specs,
      description: description,
    }
  })
}

// 生成瀑布流商品数据
const generateWaterfallGoods = (
  category: string,
  startIndex: number,
  count: number,
): WaterfallGoodsItem[] => {
  const titles = [
    '新鲜有机西红柿 自然成熟',
    '精选苹果 脆甜多汁',
    '新鲜黄瓜 翠绿爽脆',
    '优质香蕉 香甜软糯',
    '有机胡萝卜 营养丰富',
    '新鲜草莓 酸甜可口',
    '精选土豆 粉糯香甜',
    '新鲜橙子 汁多味甜',
    '有机白菜 嫩绿新鲜',
    '优质猪肉 新鲜切割',
    '深海带鱼 肉质鲜嫩',
    '精选大米 颗粒饱满',
    '纯正花生油 压榨工艺',
    '休闲零食 多种口味',
    '纯净矿泉水 天然水源',
  ]

  const colors = [
    '#FF6B6B',
    '#4ECDC4',
    '#45B7D1',
    '#96CEB4',
    '#FFEAA7',
    '#DDA0DD',
    '#98D8C8',
    '#F7DC6F',
    '#BB8FCE',
    '#85C1E9',
    '#F8C471',
    '#82E0AA',
    '#F1948A',
    '#85C1E9',
    '#D7BDE2',
  ]

  return Array.from({ length: count }, (_, i) => {
    const index = startIndex + i
    const randomPrice = Math.floor(Math.random() * 50) + 5 // 食品价格5-55元
    const randomOriginalPrice =
      Math.random() > 0.3 ? randomPrice + Math.floor(Math.random() * 20) + 5 : null
    const randomSales =
      category === 'sales'
        ? Math.floor(Math.random() * 5000) + 1000 // 销量页面显示更高销量
        : Math.floor(Math.random() * 2000) + 10
    const randomHeight = Math.floor(Math.random() * 100) + 150

    return {
      id: `${category}_${index}`,
      title: titles[index % titles.length],
      price: randomPrice,
      originalPrice: randomOriginalPrice,
      color: colors[index % colors.length],
      sales: randomSales,
      isLiked: Math.random() > 0.7,
      imageHeight: randomHeight,
    }
  })
}

// 模拟API请求获取商品数据
const fetchGoodsData = async (
  category: string,
  page: number,
  size: number,
  append: boolean = false,
) => {
  return new Promise((resolve) => {
    setTimeout(
      () => {
        // 动态生成数据，每次请求生成指定数量的商品
        const startIndex = (page - 1) * size
        const data = generateMockGoods(category, startIndex, size)

        // 模拟总共有60个商品
        const totalCount = 60
        const hasMore = startIndex + size < totalCount

        resolve({
          data,
          hasMore,
          total: totalCount,
        })
      },
      append ? 300 : 200,
    ) // 预加载时使用较长延迟，减少界面抖动；初始加载快速响应
  })
}

// 跳转到搜索页面
const goToSearch = () => {
  uni.navigateTo({
    url: '/pages/user/search',
  })
}

// 商品切换处理
const handleGoodsChange = (e: any) => {
  const newIndex = e.detail.current

  // 检查是否有效索引，防止超出范围
  if (newIndex >= 0 && newIndex < goodsList.value.length) {
    currentGoodsIndex.value = newIndex

    // 如果在推荐页面，记录当前索引
    if (activeFilter.value === 'recommend') {
      recommendGoodsIndex.value = newIndex
    }

    console.log('商品切换:', newIndex)

    // 检查是否需要预加载更多数据
    checkPreloadData()
  } else {
    console.log('无效索引:', newIndex)
  }
}

// 检查并预加载数据
const checkPreloadData = () => {
  // 已经在加载中或没有更多数据，不触发预加载
  if (isLoading.value || !hasMore.value) return

  // 当前索引
  const currentIndex = currentGoodsIndex.value
  const totalCount = goodsList.value.length

  // 边界检查，确保索引有效
  if (currentIndex < 0) {
    currentGoodsIndex.value = 0
    return
  }

  if (currentIndex >= totalCount) {
    currentGoodsIndex.value = totalCount - 1
    return
  }

  // 当滚动到倒数第5个元素时，开始加载更多
  if (currentIndex >= totalCount - 5 && hasMore.value) {
    console.log(`预加载触发: 当前索引=${currentIndex}, 总数=${totalCount}`)
    loadGoodsData(activeFilter.value, currentPage.value + 1, true)
  }
}

// 加载商品数据
const loadGoodsData = async (
  category: string,
  page: number = 1,
  append: boolean = false,
  resetIndex: boolean = true,
) => {
  if (isLoading.value) return

  isLoading.value = true
  try {
    console.log(`开始加载数据: ${category}, 页码=${page}, 追加=${append}`)
    const result = (await fetchGoodsData(category, page, pageSize, append)) as any

    setTimeout(() => {
      if (append) {
        // 追加数据到列表末尾
        goodsList.value = [...goodsList.value, ...result.data]
        console.log(
          `预加载完成，第${page}页，新增${result.data.length}个商品，总数：${goodsList.value.length}`,
        )
      } else {
        // 初始加载或切换分类，直接替换整个列表
        goodsList.value = result.data
        // 只在需要时重置当前索引
        if (resetIndex) {
          currentGoodsIndex.value = 0
          if (activeFilter.value === 'recommend') {
            recommendGoodsIndex.value = 0
          }
        }
        console.log(`初始加载完成，第${page}页，加载${result.data.length}个商品`)
      }

      hasMore.value = result.hasMore
      currentPage.value = page

      isLoading.value = false
    }, 100)
  } catch (error) {
    console.error('加载商品数据失败:', error)
    isLoading.value = false
  }
}

// 筛选处理
const handleFilterChange = async ({ name }: { name: string }) => {
  console.log('分类切换:', name)

  if (name === 'recommend') {
    // 推荐使用原有的swiper
    // 只在数据为空时重置状态，否则恢复之前的位置
    if (goodsList.value.length === 0) {
      currentGoodsIndex.value = 0
      recommendGoodsIndex.value = 0
      currentPage.value = 1
      hasMore.value = true
      // 加载推荐数据，根据当前排序条件
      await loadGoodsData('comprehensive')
    } else {
      // 恢复之前的浏览位置
      currentGoodsIndex.value = recommendGoodsIndex.value
      console.log('恢复推荐页面位置:', recommendGoodsIndex.value)
    }
  } else {
    // 其他分类使用瀑布流
    await loadWaterfallGoods(name)
  }
}

// 商品点击处理
const handleGoodsClick = (index: number, item: any) => {
  console.log('商品点击:', index, item)
  // 跳转到商品详情页
  uni.navigateTo({
    url: `/pages/user/goods-detail?id=${item.id}&title=${encodeURIComponent(item.title)}`,
  })
}

// 加载瀑布流商品数据
const loadWaterfallGoods = async (category: string, page: number = 1, append: boolean = false) => {
  if (waterfallLoading.value) return

  waterfallLoading.value = true
  try {
    // 模拟网络请求延迟
    await new Promise((resolve) => setTimeout(resolve, 500))

    const newGoods = generateWaterfallGoods(category, (page - 1) * 10, 10)

    if (append) {
      waterfallGoodsList.value.push(...newGoods)
    } else {
      waterfallGoodsList.value = newGoods
      waterfallCurrentPage.value = 1
    }

    waterfallCurrentPage.value = page
    // 模拟数据有限，加载到50个商品后停止
    waterfallHasMore.value = waterfallGoodsList.value.length < 50
  } catch (error) {
    console.error('加载瀑布流商品数据失败:', error)
  } finally {
    waterfallLoading.value = false
  }
}

// 瀑布流商品点击处理
const handleWaterfallGoodsClick = (item: WaterfallGoodsItem) => {
  uni.navigateTo({
    url: `/pages/user/goods-detail?id=${item.id}&title=${encodeURIComponent(item.title)}`,
  })
}

// 瀑布流点赞处理
const handleWaterfallLikeClick = (item: WaterfallGoodsItem) => {
  const index = waterfallGoodsList.value.findIndex((goods) => goods.id === item.id)
  if (index !== -1) {
    waterfallGoodsList.value[index].isLiked = !waterfallGoodsList.value[index].isLiked
    uni.showToast({
      title: waterfallGoodsList.value[index].isLiked ? '已收藏' : '已取消收藏',
      icon: 'success',
      duration: 1000,
    })
  }
}

// 瀑布流触底加载更多
const handleWaterfallReachBottom = () => {
  if (!waterfallLoading.value && waterfallHasMore.value) {
    loadWaterfallGoods(activeFilter.value, waterfallCurrentPage.value + 1, true)
  }
}

// 转换瀑布流商品数据为列表商品数据
const convertToListGoods = (waterfallGoods: WaterfallGoodsItem[]) => {
  return waterfallGoods.map((item) => ({
    id: item.id,
    title: item.title,
    price: item.price,
    originalPrice: item.originalPrice,
    sales: item.sales,
    isLiked: item.isLiked,
    color: item.color,
  }))
}

// 列表模式商品点击处理
const handleListGoodsClick = (item: any) => {
  uni.navigateTo({
    url: `/pages/user/goods-detail?id=${item.id}&title=${encodeURIComponent(item.title)}`,
  })
}

// 列表模式点赞处理
const handleListLikeClick = (item: any) => {
  const index = waterfallGoodsList.value.findIndex((goods) => goods.id === item.id)
  if (index !== -1) {
    waterfallGoodsList.value[index].isLiked = !waterfallGoodsList.value[index].isLiked
    uni.showToast({
      title: waterfallGoodsList.value[index].isLiked ? '已收藏' : '已取消收藏',
      icon: 'success',
      duration: 1000,
    })
  }
}

// 显示模式切换处理
const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'grid' ? 'list' : 'grid'
}

// 跳转到商户主页
const goToMerchantHome = (item: any) => {
  // 使用商品ID作为商户ID（实际项目中应该有独立的商户ID）
  const merchantId = item.id.split('_')[1] || '1'
  uni.navigateTo({
    url: `/pages/user/merchant-home?id=${merchantId}&name=${encodeURIComponent(item.shopName)}`,
  })
}

// 处理位置点击
const handleLocationClick = () => {
  uni.showToast({
    title: '选择位置',
    icon: 'none',
  })
}

onMounted(async () => {
  // 初始化数据
  console.log('用户首页初始化')

  // 默认加载推荐数据
  currentGoodsIndex.value = 0
  await loadGoodsData('comprehensive', 1, false, true) // 初始化时需要重置索引

  // 确保在数据加载完成后，重新设置一次索引
  setTimeout(() => {
    if (goodsList.value.length > 0) {
      // 强制更新当前索引，确保显示第一个商品
      currentGoodsIndex.value = 0
      recommendGoodsIndex.value = 0
    }
  }, 200)
})
</script>

<style lang="scss" scoped>
.home-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  width: 100vw;
  overflow: hidden;
}

.header-container {
  background: linear-gradient(135deg, #61b7fc 0%, transparent 100%);
  // background: linear-gradient(to right, #ff4757, #ff3742);
  width: 100%;
  box-sizing: border-box;
}

.search-header {
  padding: 5px 8px 8px 0px;
  background: transparent;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-container {
  display: flex;
  align-items: center;
  background-color: rgb(255, 255, 255);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: none;
  box-shadow: 0 2px 8px rgba(100, 181, 246, 0.15);
  margin-left: 20px;
  padding: 0 4px 0 16px;
  height: 40px;
  flex: 1;

  // 推荐页面时搜索框铺满宽度
  &.recommend-mode {
    margin-right: 20px; // 正常右边距
  }

  // 其他标签页时正常宽度，flex会自动调整
  &.other-mode {
    margin-right: 0; // 无右边距，为按钮留出空间
  }
}

.search-input-fake {
  flex: 1;
  display: flex;
  align-items: center;
  height: 100%;
  padding-left: 0;
}

.search-placeholder {
  font-size: 14px;
  color: #999;
}

.search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  height: 32px;
  background-color: #1890ff;
  border-radius: 16px;
  transition: all 0.2s;

  &:active {
    background-color: #40a9ff;
    transform: scale(0.95);
  }
}

.search-text {
  font-size: 14px;
  color: #e7e7e7;
  font-weight: 500;
}

.page-header {
  background: transparent;
  padding: 12px 20px 6px 20px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.filter-bar {
  background-color: #fff;
  border-bottom: 1px solid #eee;
  width: 100%;
  box-sizing: border-box;
}

.swiper-container {
  width: 100%;
  height: 80vh;
  display: flex;
  flex-direction: column;
}

.goods-swiper {
  width: 100%;
  height: 100%;
}

.swiper-item-container {
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}

.goods-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: relative;
}

.goods-image-wrapper {
  width: 100%;
  height: 50vh; /* 从45vh增加到50vh */
  position: relative;
  overflow: visible; /* 允许内容溢出，以便卡片可以覆盖图片 */
}

.goods-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.goods-details {
  padding: 16px 20px;
  margin-top: 95px; /* 从50px调整到95px，与卡片的bottom值匹配 */
  background-color: #fff;
  flex: 1;
  overflow-y: auto; /* 允许滚动 */
}

.description-section {
  margin-bottom: 16px;
}

.waterfall-scroll-container {
  height: 80vh;
  overflow: auto;
}

.waterfall-content {
  padding: 10px;
}

// 分类容器样式
.category-container {
  height: 80vh;
  display: flex;
  flex-direction: column;
}

.category-container .waterfall-scroll-container {
  flex: 1;
  height: auto;
}

// 隐藏 wot-design-uni tabs 横向滚动条
:deep(.wd-tabs__nav-wrap) {
  ::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }
}

// 也可以使用更通用的方式隐藏所有滚动条
:deep(.wd-tabs) {
  ::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }
}

// 搜索栏显示模式切换按钮
.header-view-toggle-button {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 2px 8px rgba(100, 181, 246, 0.15);
  opacity: 1;
  transform: translateX(0) scale(1);
  pointer-events: auto;

  .iconfont-sys {
    font-size: 16px;
    color: #666;
  }

  &:active {
    transform: scale(0.95);
  }

  // 显示状态
  &.show {
    opacity: 1;
    transform: translateX(0) scale(1);
    pointer-events: auto;
  }
}

// 加载状态样式
.loading-item,
.no-more-item {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-container,
.no-more-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.no-more-text {
  font-size: 14px;
  color: #999;
  font-weight: 400;
}
</style>
