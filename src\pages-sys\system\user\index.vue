<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '用户管理',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="user-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      title="用户管理"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <!-- 搜索区域 - 放在顶部 -->
      <view class="search-section">
        <!-- 第一行 - 两个输入框 -->
        <view class="search-row">
          <view class="search-item">
            <text class="search-label">关键词：</text>
            <wd-input
              class="search-input"
              placeholder="搜索用户名、邮箱或电话"
              v-model="searchKeyword"
              clearable
              @clear="handleSearch"
            />
          </view>
          <view class="search-item">
            <text class="search-label">真实姓名：</text>
            <wd-input
              class="search-input"
              placeholder="请输入真实姓名"
              v-model="searchRealName"
              clearable
              @clear="handleSearch"
            />
          </view>
        </view>

        <!-- 第二行 - 两个下拉框 -->
        <view class="search-row">
          <view class="search-item">
            <text class="search-label">状态：</text>
            <wd-select-picker
              class="search-select"
              v-model="searchStatusValue"
              :columns="statusOptions"
              type="radio"
              :show-confirm="false"
              @change="onStatusChange"
            />
          </view>
          <view class="search-item">
            <text class="search-label">性别：</text>
            <wd-select-picker
              class="search-select"
              v-model="searchGenderValue"
              :columns="genderOptions"
              type="radio"
              :show-confirm="false"
              @change="onGenderChange"
            />
          </view>
        </view>
      </view>

      <!-- 按钮操作栏 -->
      <view class="button-section">
        <view class="left-buttons">
          <wd-button type="success" size="small" @click="addUser">新增</wd-button>
          <wd-button type="info" size="small" icon="refresh" @click="refreshData"></wd-button>
        </view>
        <view class="right-buttons">
          <wd-button type="primary" size="small" @click="handleSearch">搜索</wd-button>
          <wd-button plain size="small" @click="resetSearch">重置</wd-button>
        </view>
      </view>

      <!-- 主要内容区域 -->
      <view class="main-wrapper">
        <!-- 左侧角色列表 -->
        <view class="sidebar-container">
          <wd-sidebar
            v-model="selectedRoleIndex"
            @change="handleRoleChange"
            custom-class="custom-sidebar"
          >
            <!-- 全部用户选项 -->
            <wd-sidebar-item
              :value="0"
              label="全部用户"
              :badge="alltotalCount > 0 ? alltotalCount : null"
              :badge-props="{ type: 'primary' }"
            />
            <!-- 未分配角色选项 -->
            <wd-sidebar-item
              :value="1"
              label="未分配角色"
              :badge="unassignedUserCount > 0 ? unassignedUserCount : null"
              :badge-props="{ type: 'primary' }"
            />
            <!-- 角色选项 -->
            <wd-sidebar-item
              v-for="(role, index) in roles"
              :key="role.id"
              :value="index + 2"
              :label="role.name"
              :badge="role.user_count > 0 ? role.user_count : null"
              :badge-props="{ type: 'primary' }"
            />
          </wd-sidebar>
        </view>

        <!-- 右侧用户内容区域 -->
        <view class="content-container">
          <!-- 用户列表区域 -->
          <view class="user-list-section">
            <view class="user-list-card">
              <!-- 加载状态 -->
              <view v-if="loading" class="loading-state">
                <wd-loading />
                <text class="loading-text">加载中...</text>
              </view>

              <!-- 用户列表 -->
              <view v-else-if="users.length > 0" class="user-list">
                <view
                  v-for="user in users"
                  :key="user.id"
                  class="user-item"
                  @click.stop="viewUserDetail(user)"
                >
                  <!-- 用户头像 -->
                  <view class="user-avatar">
                    <wd-img
                      v-if="getUserAvatar(user)"
                      :src="getUserAvatar(user)"
                      :width="50"
                      :height="50"
                      round
                      mode="aspectFill"
                      custom-class="user-avatar-img"
                    >
                      <template #error>
                        <view class="avatar-placeholder">
                          <wd-icon name="user-circle" size="40" color="#ccc" />
                        </view>
                      </template>
                      <template #loading>
                        <view class="avatar-placeholder">
                          <wd-icon name="user-circle" size="40" color="#ddd" />
                        </view>
                      </template>
                    </wd-img>
                    <view v-else class="avatar-placeholder">
                      <wd-icon name="user-circle" size="40" color="#ccc" />
                    </view>
                  </view>

                  <!-- 用户信息 -->
                  <view class="user-info">
                    <view class="user-main">
                      <text class="user-name">{{ user.username }}</text>
                      <text class="real-name" v-if="user.real_name">{{ user.real_name }}</text>
                    </view>
                    <view class="user-details">
                      <text class="phone" v-if="user.phone">{{ user.phone }}</text>
                    </view>
                    <view class="user-status">
                      <text
                        class="status-tag"
                        :class="{
                          'status-active': user.status === 1,
                          'status-inactive': user.status === 2,
                          'status-locked': user.status === 3,
                        }"
                      >
                        {{ getStatusText(user.status) }}
                      </text>
                      <text
                        v-if="
                          user.gender !== null && user.gender !== undefined && user.gender !== 0
                        "
                        class="gender-tag"
                      >
                        {{ getGenderText(user.gender) }}
                      </text>
                    </view>
                  </view>

                  <!-- 操作区域 -->
                  <view class="user-actions-wrapper">
                    <!-- 右箭头 -->
                    <wd-icon name="arrow-right" size="20" color="#999" class="arrow-icon" />

                    <!-- 操作按钮 -->
                    <view class="user-actions" @click.stop>
                      <wd-button
                        type="icon"
                        icon="edit"
                        size="small"
                        custom-class="action-btn"
                        @click="editUser(user)"
                      />
                      <wd-button
                        type="icon"
                        size="small"
                        icon="delete"
                        custom-class="action-btn"
                        @click="deleteUser(user)"
                        v-if="user.username !== 'admin'"
                      />
                    </view>
                  </view>
                </view>
              </view>

              <!-- 空状态 -->
              <view v-else-if="!loading" class="empty-state">
                <wd-status-tip image="content" :tip="getEmptyTip()" />
              </view>

              <!-- 分页组件 -->
              <view v-if="totalCount > 0" class="pagination-wrapper">
                <wd-pagination
                  v-model="currentPage"
                  :total="totalCount"
                  :page-size="pageSize"
                  @change="handlePageChange"
                  show-icon
                  show-message
                />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import {
  getSystemUsersApi,
  getSystemUnassignedRoleUsersApi,
  getUnassignedUserCountApi,
  deleteSystemUserApi,
  changeUserStatusApi,
  type ISysUserPageRequest,
  type ISysUserListResponse,
} from '@/api/sys/systemUserApi'
import { getSystemRoleSimpleApi, type ISysRoleSimpleResponse } from '@/api/sys/systemRoleApi'

defineOptions({
  name: 'UserManagement',
})

// 角色相关状态
const roles = ref<ISysRoleSimpleResponse[]>([])
const selectedRoleIndex = ref<number>(0)
const unassignedUserCount = ref<number>(0)
const selectedRole = computed(() => {
  // 索引0是"全部用户"，返回null
  if (selectedRoleIndex.value === 0) return null
  // 索引1是"未分配角色"，返回特殊标识
  if (selectedRoleIndex.value === 1) return 'unassigned'
  // 其他索引减2获取对应角色
  return selectedRoleIndex.value > 1 ? roles.value[selectedRoleIndex.value - 2] : null
})

// 搜索参数
const searchKeyword = ref('')
const searchRealName = ref('')
const searchStatusValue = ref('')
const searchGenderValue = ref('')

// 用户数据
const users = ref<ISysUserListResponse[]>([])
const totalCount = ref(0)
const alltotalCount = ref(0)
const loading = ref(false)

// 状态选项
const statusOptions = ref([
  { label: '全部', value: '' },
  { label: '启用', value: 1 },
  { label: '禁用', value: 2 },
  { label: '锁定', value: 3 },
])

// 性别选项
const genderOptions = ref([
  { label: '全部', value: '' },
  { label: '男', value: 1 },
  { label: '女', value: 2 },
  { label: '未知', value: 3 },
])

// 分页相关状态
const currentPage = ref(1)
const pageSize = ref(10)

// 首次加载标记
const isFirstLoad = ref(true)

// 获取未分配角色用户数量
const fetchUnassignedUserCount = async () => {
  try {
    const result = await getUnassignedUserCountApi()
    if (result.code === 200 && result.data) {
      unassignedUserCount.value = result.data.unassigned_user_count || 0
    } else {
      unassignedUserCount.value = 0
    }
    console.log('获取未分配角色用户数量成功:', unassignedUserCount.value)
  } catch (error) {
    console.error('获取未分配角色用户数量失败:', error)
    unassignedUserCount.value = 0
  }
}

// 获取角色列表
const fetchRoles = async () => {
  try {
    const result = await getSystemRoleSimpleApi()
    if (result.code === 200 && result.data) {
      roles.value = result.data
      console.log('获取角色列表成功:', result.data)

      // 默认选择"全部用户"
      selectedRoleIndex.value = 0
      await fetchUsers()
    }
  } catch (error) {
    console.error('获取角色列表失败:', error)
    uni.showToast({
      title: '获取角色列表失败',
      icon: 'none',
    })
  }
}

// 获取用户列表
const fetchUsers = async () => {
  loading.value = true
  try {
    // 构建查询参数对象
    const queryParams: any = {
      page: currentPage.value,
      page_size: pageSize.value,
    }

    // 判断选择的是什么类型
    if (selectedRole.value === 'unassigned') {
      // 未分配角色的用户，不需要传递role_id
    } else if (selectedRole.value && typeof selectedRole.value === 'object') {
      // 选择了具体角色
      queryParams.role_id = selectedRole.value.id
    }
    // 如果是"全部用户"（selectedRole.value === null），不传递role_id

    // 处理搜索关键词
    if (searchKeyword.value.trim()) {
      // 如果是邮箱格式，搜索邮箱
      if (searchKeyword.value.includes('@')) {
        queryParams.email = searchKeyword.value.trim()
      }
      // 如果是纯数字，搜索手机号
      else if (/^\d+$/.test(searchKeyword.value.trim())) {
        queryParams.phone = searchKeyword.value.trim()
      }
      // 否则搜索用户名
      else {
        queryParams.username = searchKeyword.value.trim()
      }
    }

    // 设置其他搜索参数
    if (searchRealName.value.trim()) {
      queryParams.real_name = searchRealName.value.trim()
    }
    if (searchStatusValue.value) {
      queryParams.status = Number(searchStatusValue.value)
    }
    if (searchGenderValue.value !== '') {
      queryParams.gender = Number(searchGenderValue.value)
    }

    console.log('获取用户列表参数:', queryParams)

    // 根据选择的角色类型调用不同的API
    let result
    if (selectedRole.value === 'unassigned') {
      result = await getSystemUnassignedRoleUsersApi(queryParams)
    } else {
      result = await getSystemUsersApi(queryParams)
    }

    users.value = result.data.items || []
    totalCount.value = result.data.total || 0

    // 如果选择的是"全部用户"，将总数赋值给alltotalCount
    if (selectedRoleIndex.value === 0) {
      alltotalCount.value = totalCount.value
    }

    console.log('获取用户列表成功:', result.data)
  } catch (error) {
    console.error('获取用户列表失败:', error)
    uni.showToast({
      title: '获取用户列表失败',
      icon: 'none',
    })
    users.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 角色切换处理
const handleRoleChange = async ({ value }: { value: number }) => {
  console.log('切换角色:', value)
  if (value === 0) {
    console.log('选择全部用户')
  } else if (value === 1) {
    console.log('选择未分配角色')
  } else {
    console.log('选择角色:', roles.value[value - 2])
  }
  selectedRoleIndex.value = value
  currentPage.value = 1
  await fetchUsers()
}

// 获取状态显示文本
const getStatusText = (status: number) => {
  switch (status) {
    case 1:
      return '启用'
    case 2:
      return '禁用'
    case 3:
      return '锁定'
    default:
      return '未知'
  }
}

// 获取性别显示文本
const getGenderText = (gender?: number) => {
  switch (gender) {
    case 1:
      return '男'
    case 2:
      return '女'
    case 3:
      return '未知'
    default:
      return '-'
  }
}

// 处理搜索
const handleSearch = async () => {
  console.log('执行搜索')
  currentPage.value = 1
  await fetchUsers()
}

// 状态选择器变化事件
const onStatusChange = ({ value }: { value: number | string }) => {
  console.log('状态变化:', value)
  searchStatusValue.value = value as string
  handleSearch()
}

// 性别选择器变化事件
const onGenderChange = ({ value }: { value: number | string }) => {
  console.log('性别变化:', value)
  searchGenderValue.value = value as string
  handleSearch()
}

// 重置搜索
const resetSearch = async () => {
  searchKeyword.value = ''
  searchRealName.value = ''
  searchStatusValue.value = ''
  searchGenderValue.value = ''
  currentPage.value = 1

  await fetchUsers()

  await uni.showToast({
    title: '已重置搜索条件',
    icon: 'success',
  })
  await fetchRoles()
  await fetchUnassignedUserCount()
}

// 处理分页变化
const handlePageChange = async ({ value }: { value: number }) => {
  console.log('切换到第', value, '页')
  currentPage.value = value
  await fetchUsers()
}

// 刷新数据
const refreshData = async () => {
  console.log('刷新用户数据')
  await fetchRoles()
  await fetchUnassignedUserCount()
  uni.showToast({
    title: '数据已刷新',
    icon: 'success',
  })
}

// 新增用户
const addUser = () => {
  console.log('新增用户')
  uni.navigateTo({
    url: '/pages-sys/system/user/user-form?mode=add',
  })
}

// 编辑用户
const editUser = (user: ISysUserListResponse) => {
  console.log('编辑用户:', user)
  uni.navigateTo({
    url: `/pages-sys/system/user/user-form?mode=edit&userId=${user.id}`,
  })
}

// 查看用户详情
const viewUserDetail = (user: ISysUserListResponse) => {
  console.log('查看用户详情:', user)

  uni.navigateTo({
    url: `/pages-sys/system/user/user-detail?userId=${user.id}&userName=${encodeURIComponent(
      user.username,
    )}`,
  })
}

// 删除用户
const deleteUser = async (user: ISysUserListResponse) => {
  console.log('删除用户:', user)

  uni.showModal({
    title: '确认删除',
    content: `确定要删除用户 "${user.username}" 吗？此操作不可恢复。`,
    success: async (res) => {
      if (res.confirm) {
        try {
          await deleteSystemUserApi(user.id)

          uni.showToast({
            title: '删除成功',
            icon: 'success',
          })

          // 刷新列表
          await fetchUsers()
        } catch (error) {
          console.error('删除用户失败:', error)
          uni.showToast({
            title: '删除失败',
            icon: 'none',
          })
        }
      }
    },
  })
}

// 返回功能
const handleBack = () => {
  console.log('返回功能')
  uni.navigateBack()
}

// 获取空状态提示文本
const getEmptyTip = () => {
  if (selectedRoleIndex.value === 0) {
    return '暂无用户数据'
  }
  if (selectedRoleIndex.value === 1) {
    return '暂无未分配角色的用户'
  }
  if (selectedRole.value && typeof selectedRole.value === 'object') {
    return `角色 '${selectedRole.value.name}' 下暂无用户`
  }
  return '请选择角色查看用户'
}

// 获取用户头像
const getUserAvatar = (user: ISysUserListResponse) => {
  return user.avatar || null
}

// 页面初始化
onMounted(async () => {
  console.log('用户管理页面挂载完成')
  await fetchRoles() // 先获取角色列表
  await fetchUnassignedUserCount() // 获取未分配角色用户数量
  isFirstLoad.value = false
})

// 页面显示时刷新数据
onShow(async () => {
  console.log('用户管理页面显示')
  if (!isFirstLoad.value) {
    console.log('从其他页面返回，刷新用户数据')
    await fetchUsers()
    await fetchUnassignedUserCount() // 刷新未分配角色用户数量
  }
})
</script>

<style lang="scss" scoped>
.user-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  position: relative;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

// 标题文字设为白色
:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

// 自定义返回按钮样式
:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  background-color: #ffffff;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.main-wrapper {
  display: flex;
  flex: 1;
  flex-direction: row;
  overflow: hidden;
}

.sidebar-container {
  width: 130px;
  min-width: 130px;
  max-width: 10px;
  background-color: #fff;
  overflow-y: auto;
  flex-shrink: 0;
}

// 自定义侧边栏样式
:deep(.custom-sidebar) {
  height: 100%;
  width: 100%;
}

// 修复Sidebar内容宽度问题
:deep(.sidebar-container .wd-sidebar) {
  width: 100% !important;
  height: 100% !important;
}

:deep(.sidebar-container .wd-sidebar-item) {
  width: 100% !important;
  padding: 12px 16px !important;
  min-height: 48px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

:deep(.sidebar-container .wd-sidebar-item__label) {
  flex: 1 !important;
  text-align: left !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  font-size: 14px !important;
  color: #333 !important;
}

:deep(.sidebar-container .wd-sidebar-item--active .wd-sidebar-item__label) {
  color: #4285f4 !important;
  font-weight: 600 !important;
}

:deep(.sidebar-container .wd-sidebar-item__badge) {
  margin-left: 8px !important;
  flex-shrink: 0 !important;
}

.content-container {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #ffffff;
  overflow-y: auto;
}

.search-section {
  padding: 12px 16px;
  background-color: #ffffff;
  border-bottom: 1px solid #eee;
}

.button-section {
  padding: 12px 16px;
  background-color: #ffffff;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.search-row {
  display: flex;
  gap: 20px;
  align-items: flex-end;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.search-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1px;
}

.search-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
  min-width: 45px;
  line-height: 1.2;
  display: inline-block;
  vertical-align: middle;
}

.search-input {
  width: 120px;
  height: 28px;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  background-color: white !important;
  overflow: hidden;
}

:deep(.search-input .wd-input) {
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

:deep(.search-input .wd-input__inner) {
  padding: 6px 10px;
  font-size: 12px;
  height: 28px;
  box-sizing: border-box;
  border: none;
  background-color: transparent;
}

:deep(.search-input .wd-input:focus-within) {
  border-color: #4285f4;
}

:deep(.search-input .wd-input__inner:focus) {
  outline: none;
}

:deep(.search-select .wd-select-picker__label) {
  font-size: 12px !important;
  line-height: 1.2 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.picker-text {
  font-size: 12px;
}

.left-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.right-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 12px;
}

.user-list-card {
  background-color: transparent;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow-y: auto;
}

.user-list {
  background-color: transparent;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
  flex: 1;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s;
  min-width: 0; /* 确保flex容器不会溢出 */

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f5f7fa;
  }
}

.user-avatar {
  width: 50px;
  height: 50px;
  margin-right: 12px;
  flex-shrink: 0;
}

.user-avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 50%;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-right: 12px;
  min-width: 0; /* 确保flex子项可以收缩 */
  overflow: hidden; /* 防止内容溢出 */
}

.user-main {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 2px;
  width: 100%;
  min-width: 0; /* 确保flex容器可以收缩 */
}

.user-name {
  font-size: 15px;
  font-weight: bold;
  color: #333;
}

.real-name {
  font-size: 13px;
  color: #666;
  max-width: 60px; /* 限制最大宽度 */
  flex-shrink: 1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.user-details {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.user-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-tag {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  white-space: nowrap;

  &.status-active {
    background-color: #e8f5e8;
    color: #4caf50;
  }

  &.status-inactive {
    background-color: #ffebee;
    color: #f44336;
  }

  &.status-locked {
    background-color: #fff9c4;
    color: #f57f17;
  }
}

.gender-tag {
  font-size: 11px;
  color: #666;
  background-color: #e3f2fd;
  padding: 2px 6px;
  border-radius: 10px;
}

.user-actions-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  margin-left: auto;
  flex-shrink: 0; /* 防止操作按钮被压缩 */
  min-width: 40px; /* 确保有足够空间显示按钮 */
}

.arrow-icon {
  transition: transform 0.2s;
  position: relative;
  top: -8px;

  .user-item:hover & {
    transform: translateX(3px);
  }
}

.user-actions {
  display: flex;
  gap: 6px;
  justify-content: center;
  align-items: center;
}

:deep(.action-btn) {
  width: 32px !important;
  height: 32px !important;
  padding: 0 !important;
  min-width: 32px !important;
  border-radius: 50% !important;
  background-color: #f5f5f5 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  &:hover {
    background-color: #eeeeee !important;
  }

  .wd-icon {
    color: #666666 !important;
    font-size: 14px !important;
    margin: 0 !important;
  }
}

.pagination-wrapper {
  padding: 12px 0;
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
  margin-top: 12px;
}

.role-tag {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;
}

.status-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;

  &.active {
    background-color: #e8f5e8;
    color: #4caf50;
  }

  &.inactive {
    background-color: #ffebee;
    color: #f44336;
  }

  &.locked {
    background-color: #fff9c4;
    color: #f57f17;
  }
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
}

.loading-text {
  color: #999;
  font-size: 14px;
  margin-top: 10px;
}
</style>
