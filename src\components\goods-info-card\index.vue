<template>
  <view class="goods-info-card">
    <view class="goods-price-row">
      <view class="goods-price">
        <text class="price-label">参考价</text>
        <text class="goods-price-symbol">¥</text>
        {{ price }}
      </view>
      <text class="goods-sales">销量: {{ sales }}</text>
    </view>
    <view class="goods-title">{{ title }}</view>
    <view class="goods-specs">
      <text
        v-for="(spec, specIndex) in displaySpecs"
        :key="specIndex"
        class="spec-tag"
      >
        {{ spec }}
      </text>
    </view>
    <view class="goods-description-in-card">{{ description }}</view>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

// 定义props
interface Props {
  price: string | number
  sales: number
  title: string
  specs: string[]
  description: string
}

const props = defineProps<Props>()

// 只显示前2个规格
const displaySpecs = computed(() => props.specs.slice(0, 2))
</script>

<style lang="scss" scoped>
.goods-info-card {
  position: absolute;
  bottom: -95px; 
  left: 16px;
  right: 16px;
  padding: 16px 16px;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  z-index: 20;
}

.goods-price-row {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 8px;
  padding-right: 4px;
}

.goods-price {
  font-size: 22px;
  color: #ff4142;
  font-weight: 700;
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.price-label {
  font-size: 12px;
  color: #999;
  font-weight: 400;
  margin-right: 0;
}

.goods-price-symbol {
  font-size: 14px;
  margin-right: 1px;
  font-weight: 600;
}

.goods-title {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}

.goods-specs {
  display: flex;
  gap: 6px;
  margin-bottom: 8px;
}

.spec-tag {
  font-size: 10px;
  color: #666;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 8px;
  white-space: nowrap;
}

.goods-sales {
  font-size: 13px;
  color: #666;
  white-space: nowrap;
  flex-shrink: 0;
  margin-right: 0;
}

.goods-description-in-card {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}
</style> 