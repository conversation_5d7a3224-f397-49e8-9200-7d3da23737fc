<template>
  <wd-tabbar
    :model-value="currentPage"
    @change="handleTabChange"
    fixed
    placeholder
    safeAreaInsetBottom
    bordered
    active-color="#007aff"
    inactive-color="#999999"
  >
    <wd-tabbar-item v-for="item in tabItems" :key="item.name" :name="item.name" :title="item.title">
      <template #icon>
        <!-- 使用wot组件图标 - 没有特殊前缀的图标 -->
        <wd-icon
          v-if="!item.icon.startsWith('iconsys')"
          :name="item.icon"
          size="20px"
          :color="currentPage === item.name ? '#007aff' : '#999999'"
        />
        <!-- 使用自定义图标 - iconsys开头的图标 -->
        <view
          v-else
          class="iconfont-sys"
          :class="`${item.icon}`"
          :style="{
            fontSize: '20px',
            color: currentPage === item.name ? '#007aff' : '#999999',
          }"
        ></view>
      </template>
    </wd-tabbar-item>
  </wd-tabbar>
</template>

<script lang="ts" setup>
import { computed, onMounted, watch } from 'vue'
import { useGlobalRole, userTypeEnum } from '@/store/globalRole'
import { useMenuPermissionStore } from '@/store/menuPermission'

defineOptions({
  name: 'SysTabBar',
})

const currentRoleStore = useGlobalRole()
const menuPermissionStore = useMenuPermissionStore()

// 组件属性
const props = defineProps({
  currentPage: {
    type: String,
    required: true,
  },
})

// TabBar项接口
interface SysTabItem {
  name: string
  title: string
  icon: string
  route: string
}

// 计算TabBar项 - 完全基于动态菜单数据
const tabItems = computed<SysTabItem[]>(() => {
  // 如果没有菜单数据，返回空数组（严格权限控制）
  if (menuPermissionStore.tabBarList.length === 0) {
    console.log('没有菜单权限数据，返回空TabBar')
    return []
  }

  console.log('使用动态菜单数据，原始数据:', menuPermissionStore.tabBarList)

  // 将 ITabBarItem 转换为 SysTabItem，添加路由映射
  return menuPermissionStore.tabBarList.map((item) => {
    // 路径映射：后端路径 → 前端页面路径
    let route = item.path
    let pageName = item.name

    // 通用处理：从path中提取页面名称
    const pathSegments = item.path.split('/')
    if (pathSegments.length >= 3 && pathSegments[1] === 'pages-sys') {
      pageName = pathSegments[2] // dashboard, system, merchant
      route = item.path
    } else {
      pageName = item.path.replace(/^\//, '').split('/')[0] || 'unknown'
      route = item.path
    }

    // 确保pageName去掉下划线前缀（处理后端数据格式）
    if (pageName.startsWith('_')) {
      pageName = pageName.substring(1)
    }

    const result = {
      name: pageName, // 使用简化的页面名称，与currentPage匹配
      title: item.title,
      icon: item.icon,
      route: route,
    }

    console.log(`TabBar项转换: ${item.name} -> ${pageName}`, result)
    return result
  })
})

// 路由映射
const routeMap = computed(() => {
  const map: Record<string, string> = {}
  tabItems.value.forEach((item) => {
    map[item.name] = item.route
  })
  console.log('路由映射:', map)
  return map
})

// 页面跳转状态
let isJumping = false

// 重置跳转状态
const resetJumpingState = () => {
  isJumping = false
  console.log('跳转状态已重置')
}

// 处理TabBar切换
const handleTabChange = ({ value }: { value: string }) => {
  console.log('SysTabBar点击:', value, '当前页面:', props.currentPage)
  console.log('当前TabBar数据:', tabItems.value)
  console.log(
    '匹配检查:',
    tabItems.value.map((item) => ({
      name: item.name,
      isMatch: item.name === props.currentPage,
      title: item.title,
    })),
  )

  // 如果点击的是当前页面，不进行跳转
  if (props.currentPage === value) {
    console.log('点击当前页面，不跳转')
    return
  }

  // 防止快速连续点击
  if (isJumping) {
    console.log('正在跳转中，忽略点击')
    return
  }

  const targetRoute = routeMap.value[value]
  if (targetRoute) {
    isJumping = true
    console.log('开始跳转到:', targetRoute)

    // 使用 redirectTo 进行页面切换
    uni.redirectTo({
      url: targetRoute,
      success: () => {
        console.log('跳转成功:', targetRoute)
        resetJumpingState()
      },
      fail: (err) => {
        console.log('redirectTo失败，尝试reLaunch:', err)
        // 如果 redirectTo 失败，使用 reLaunch 作为备选
        uni.reLaunch({
          url: targetRoute,
          success: () => {
            console.log('reLaunch跳转成功:', targetRoute)
            resetJumpingState()
          },
          fail: (launchErr) => {
            console.error('所有跳转方式都失败:', launchErr)
            resetJumpingState()
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none',
            })
          },
        })
      },
    })
  } else {
    console.warn('未找到对应的路由:', value)
  }
}

// 监听角色变化，确保只在系统用户时使用
watch(
  () => currentRoleStore.getRole,
  (newRole) => {
    console.log('SysTabBar检测到角色变化:', newRole)

    if (newRole === userTypeEnum.system) {
      console.log('系统用户角色，检查菜单数据...')

      // 如果没有菜单数据，主动获取
      if (menuPermissionStore.tabBarList.length === 0) {
        console.log('菜单数据为空，主动获取...')
        menuPermissionStore
          .fetchUserMenus()
          .then(() => {
            console.log('菜单获取完成，数据:', menuPermissionStore.tabBarList)
          })
          .catch((error) => {
            console.error('菜单获取失败:', error)
          })
      }
    }
  },
  { immediate: true },
)

// 组件挂载时检查菜单数据
onMounted(() => {
  console.log('SysTabBar组件挂载')
  console.log('当前角色:', currentRoleStore.getRole)
  console.log('当前页面:', props.currentPage)
  console.log('菜单数据长度:', menuPermissionStore.tabBarList.length)

  // 如果是系统用户但没有菜单数据，主动获取
  if (
    currentRoleStore.getRole === userTypeEnum.system &&
    menuPermissionStore.tabBarList.length === 0
  ) {
    console.log('系统用户缺少菜单数据，挂载时主动获取...')
    menuPermissionStore.fetchUserMenus()
  }
})
</script>

<style scoped>
/* 系统TabBar专用样式 */
</style>
