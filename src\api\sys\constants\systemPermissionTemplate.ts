/**
 * 系统权限模板管理相关常量
 */

/**
 * 分页查询默认参数
 */
export const SysPermissionTemplatePageConstants = {
  // 默认页码
  DEFAULT_PAGE: 1,
  // 默认每页大小
  DEFAULT_PAGE_SIZE: 10,
  // 最大每页大小
  MAX_PAGE_SIZE: 100,
  // 最小每页大小
  MIN_PAGE_SIZE: 1,
} as const

/**
 * 权限类型枚举及映射
 */
export enum SysPermissionTemplateType {
  // 目录
  DIRECTORY = 1,
  // 菜单
  MENU = 2,
  // 按钮
  BUTTON = 3,
}

export const SysPermissionTemplateTypeMap = {
  [SysPermissionTemplateType.DIRECTORY]: '目录',
  [SysPermissionTemplateType.MENU]: '菜单',
  [SysPermissionTemplateType.BUTTON]: '按钮',
} as const

/**
 * 是否外链枚举及映射
 */
export enum SysPermissionTemplateIsFrame {
  // 是
  YES = 0,
  // 否
  NO = 1,
}

export const SysPermissionTemplateIsFrameMap = {
  [SysPermissionTemplateIsFrame.YES]: '是',
  [SysPermissionTemplateIsFrame.NO]: '否',
} as const

/**
 * 是否缓存枚举及映射
 */
export enum SysPermissionTemplateIsCache {
  // 缓存
  CACHE = 0,
  // 不缓存
  NO_CACHE = 1,
}

export const SysPermissionTemplateIsCacheMap = {
  [SysPermissionTemplateIsCache.CACHE]: '缓存',
  [SysPermissionTemplateIsCache.NO_CACHE]: '不缓存',
} as const

/**
 * 菜单显示状态枚举及映射
 */
export enum SysPermissionTemplateVisible {
  // 显示
  SHOW = 0,
  // 隐藏
  HIDE = 1,
}

export const SysPermissionTemplateVisibleMap = {
  [SysPermissionTemplateVisible.SHOW]: '显示',
  [SysPermissionTemplateVisible.HIDE]: '隐藏',
} as const

/**
 * 验证规则常量
 */
export const SysPermissionTemplateValidation = {
  // 权限名称
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 30,

  // 权限编码
  CODE_MIN_LENGTH: 3,
  CODE_MAX_LENGTH: 100,
  CODE_PATTERN: /^[a-z:_-]{3,100}$/,

  // 路由地址
  PATH_MAX_LENGTH: 200,

  // 组件路径
  COMPONENT_MAX_LENGTH: 200,

  // 描述
  DESCRIPTION_MAX_LENGTH: 150,

  // 备注
  REMARK_MAX_LENGTH: 255,
} as const 