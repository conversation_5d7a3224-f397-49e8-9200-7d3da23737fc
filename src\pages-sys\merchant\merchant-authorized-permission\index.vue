<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '商户授权权限管理',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="merchant-permission-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      title="商户授权权限管理"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <view class="main-layout">
        <!-- 商户授权权限管理页面内容 -->
        <view class="merchant-permission-management">
          <!-- 操作栏 -->
          <view class="action-bar">
            <!-- 搜索条件栏 -->
            <view class="search-bar">
              <view class="search-form">
                <!-- 第一行搜索条件 -->
                <view class="search-row">
                  <view class="search-item">
                    <text class="search-label">商户名称：</text>
                    <wd-input
                      class="search-input-component"
                      placeholder="请输入商户名称"
                      v-model="searchParams.merchant_name"
                      clearable
                      @clear="handleSearch"
                    />
                  </view>
                  <view class="search-item">
                    <text class="search-label">商户编码：</text>
                    <wd-input
                      class="search-input-component"
                      placeholder="请输入商户编码"
                      v-model="searchParams.merchant_code"
                      clearable
                      @clear="handleSearch"
                    />
                  </view>
                </view>

                <!-- 第二行搜索条件 -->
                <view class="search-row">
                  <view class="search-item">
                    <text class="search-label">联系电话：</text>
                    <wd-input
                      class="search-input-component"
                      placeholder="请输入联系电话"
                      v-model="searchParams.phone"
                      clearable
                      @clear="handleSearch"
                    />
                  </view>
                  <view class="search-item">
                    <text class="search-label">邮箱地址：</text>
                    <wd-input
                      class="search-input-component"
                      placeholder="请输入邮箱地址"
                      v-model="searchParams.email"
                      clearable
                      @clear="handleSearch"
                    />
                  </view>
                </view>

                <!-- 第三行搜索条件 -->
                <view class="search-row">
                  <view class="search-item">
                    <text class="search-label">商户分类：</text>
                    <wd-select-picker
                      class="search-select-picker"
                      v-model="searchParams.category_id"
                      :columns="categoryOptions"
                      type="radio"
                      :show-confirm="false"
                      @change="onCategoryChange"
                    />
                  </view>
                  <view class="search-item">
                    <text class="search-label">状态：</text>
                    <wd-select-picker
                      class="search-select-picker"
                      v-model="searchParams.status"
                      :columns="statusOptions"
                      type="radio"
                      :show-confirm="false"
                      @change="onStatusChange"
                    />
                  </view>
                </view>

                <!-- 按钮行 -->
                <view class="button-row">
                  <view class="left-buttons">
                    <wd-button
                      type="info"
                      size="small"
                      icon="refresh"
                      @click="refreshData"
                    ></wd-button>
                  </view>
                  <view class="right-buttons">
                    <wd-button type="primary" size="small" @click="handleSearch">搜索</wd-button>
                    <wd-button plain size="small" @click="resetSearch">重置</wd-button>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 商户列表 -->
          <view class="merchant-list">
            <wd-table :data="merchantList" :height="300" :stripe="true" :border="true">
              <!-- 商户名称列（固定） -->
              <wd-table-col prop="merchant_name" label="商户名称" :width="120" sortable fixed>
                <template #value="{ row }">
                  <text class="merchant-name-text">{{ row.merchant_name }}</text>
                </template>
              </wd-table-col>

              <!-- 分类名称列 -->
              <wd-table-col prop="category_name" label="分类" :width="100" align="center">
                <template #value="{ row }">
                  <text class="category-tag">{{ row.category_name || '-' }}</text>
                </template>
              </wd-table-col>

              <!-- 联系电话列 -->
              <wd-table-col prop="phone" label="联系电话" :width="120" align="center">
                <template #value="{ row }">
                  <text>{{ row.phone || '-' }}</text>
                </template>
              </wd-table-col>

              <!-- 地址列 -->
              <wd-table-col prop="address" label="地址" :width="180" align="left">
                <template #value="{ row }">
                  <text class="address-text">{{ row.address || '-' }}</text>
                </template>
              </wd-table-col>

              <!-- 状态列 -->
              <wd-table-col prop="status_desc" label="状态" :width="80" align="center">
                <template #value="{ row }">
                  <text class="status-tag" :class="getStatusClass(row.status)">
                    {{ row.status_desc }}
                  </text>
                </template>
              </wd-table-col>

              <!-- 操作列 -->
              <wd-table-col prop="actions" label="操作" :width="200" align="center">
                <template #value="{ row }">
                  <view class="action-buttons">
                    <wd-button type="primary" size="small" @click.stop="viewPermissions(row)">
                      查看权限
                    </wd-button>
                    <wd-button
                      type="warning"
                      size="small"
                      @click.stop="editMerchantPermissions(row)"
                    >
                      修改权限
                    </wd-button>
                  </view>
                </template>
              </wd-table-col>
            </wd-table>

            <!-- 空状态 -->
            <view v-if="merchantList.length === 0 && !loading" class="empty-state">
              <wd-status-tip image="search" tip="当前搜索无结果" />
            </view>

            <!-- 分页组件 -->
            <view class="pagination-wrapper" v-if="totalCount > 0">
              <wd-pagination
                v-model="currentPage"
                :total="totalCount"
                :page-size="pageSize"
                @change="handlePageChange"
                show-icon
                show-message
              />
            </view>

            <!-- 加载状态 -->
            <view v-if="loading" class="loading-state">
              <text class="loading-text">加载中...</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  getSystemMerchantsApi,
  type ISysMerchantListResponse,
  type ISysMerchantPageRequest,
} from '@/api/sys/systemMerchantApi'
import { getSystemMerchantCategorySelectItemsApi } from '@/api/sys/systemMerchantCategoryApi'
import { onShow } from '@dcloudio/uni-app'

defineOptions({
  name: 'MerchantPermissionManagement',
})

// 商户数据类型定义 - 使用真实API类型
type Merchant = ISysMerchantListResponse

// 搜索参数 - 使用真实API参数类型
const searchParams = ref<ISysMerchantPageRequest>({
  merchant_name: undefined,
  merchant_code: undefined,
  phone: undefined,
  email: undefined,
  category_id: undefined,
  status: undefined,
  page: 1,
  page_size: 10,
})

// 商户分类选项 - 从API获取
const categoryOptions = ref([{ label: '全部', value: undefined }])

// 状态选项 - 1正常营业 2临时关闭 3永久关闭
const statusOptions = ref([
  { label: '全部', value: undefined },
  { label: '正常营业', value: 1 },
  { label: '临时关闭', value: 2 },
  { label: '永久关闭', value: 3 },
])

// 数据状态
const merchantList = ref<Merchant[]>([])
const totalCount = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const loading = ref(false)

// 获取状态样式类
const getStatusClass = (status: number) => {
  switch (status) {
    case 1:
      return 'status-normal'
    case 2:
      return 'status-temporary'
    case 3:
      return 'status-permanent'
    default:
      return ''
  }
}

// 获取商户分类选项
const fetchCategoryOptions = async () => {
  try {
    const result = await getSystemMerchantCategorySelectItemsApi()

    // 重置分类选项，保留"全部分类"
    categoryOptions.value = [
      { label: '全部', value: undefined },
      ...result.data.map((item) => ({
        label: item.category_name,
        value: item.id,
      })),
    ]

    console.log('获取商户分类选项成功:', categoryOptions.value)
  } catch (error) {
    console.error('获取商户分类选项失败:', error)
    uni.showToast({
      title: '获取分类数据失败',
      icon: 'none',
    })
  }
}

// 获取商户列表 - 使用真实API
const fetchMerchants = async () => {
  loading.value = true
  try {
    // 构建查询参数对象 - 只传递有值的参数
    const queryParams: any = {
      page: currentPage.value,
      page_size: pageSize.value,
    }

    // 设置搜索参数 - 只有有值的才添加
    if (searchParams.value.merchant_name?.trim()) {
      queryParams.merchant_name = searchParams.value.merchant_name.trim()
    }
    if (searchParams.value.merchant_code?.trim()) {
      queryParams.merchant_code = searchParams.value.merchant_code.trim()
    }
    if (searchParams.value.phone?.trim()) {
      queryParams.phone = searchParams.value.phone.trim()
    }
    if (searchParams.value.email?.trim()) {
      queryParams.email = searchParams.value.email.trim()
    }
    if (searchParams.value.category_id !== undefined && searchParams.value.category_id !== '') {
      queryParams.category_id = searchParams.value.category_id
    }
    if (searchParams.value.status !== undefined && searchParams.value.status !== null) {
      queryParams.status = searchParams.value.status
    }

    console.log('获取商户列表参数:', queryParams)

    const result = await getSystemMerchantsApi(queryParams)

    merchantList.value = result.data.items
    totalCount.value = result.data.total
    // 修复：不使用API返回的page值，保持当前页面的currentPage
    // currentPage.value = result.data.page
    pageSize.value = result.data.page_size

    console.log('获取商户数据成功:', result.data)
  } catch (error) {
    console.error('获取商户数据失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = async () => {
  console.log('搜索商户:', searchParams.value)
  currentPage.value = 1
  searchParams.value.page = 1
  await fetchMerchants()
}

// 分类选择器变化事件
const onCategoryChange = ({ value }: { value: string | undefined }) => {
  console.log('分类变化:', value)
  searchParams.value.category_id = value
}

// 状态选择器变化事件
const onStatusChange = ({ value }: { value: number | undefined }) => {
  console.log('状态变化:', value)
  searchParams.value.status = value
}

// 重置搜索
const resetSearch = () => {
  searchParams.value = {
    merchant_name: undefined,
    merchant_code: undefined,
    phone: undefined,
    email: undefined,
    category_id: undefined,
    status: undefined,
    page: 1,
    page_size: 10,
  }
  currentPage.value = 1
  fetchMerchants()
}

// 处理分页变化
const handlePageChange = ({ value }: { value: number }) => {
  console.log('切换到第', value, '页')
  currentPage.value = value
  searchParams.value.page = value
  fetchMerchants()
}

// 刷新数据
const refreshData = () => {
  console.log('刷新商户数据')
  fetchMerchants()
}

// 查看商户权限
const viewPermissions = async (merchant: Merchant) => {
  console.log('查看商户权限:', merchant)

  // 跳转到查看权限页面
  uni.navigateTo({
    url: `/pages-sys/merchant/merchant-authorized-permission/view-permission?merchantId=${
      merchant.id
    }&merchantName=${encodeURIComponent(merchant.merchant_name)}`,
  })
}

// 修改商户权限
const editMerchantPermissions = async (merchant: Merchant) => {
  console.log('修改商户权限:', merchant)

  // 跳转到修改权限页面
  uni.navigateTo({
    url: `/pages-sys/merchant/merchant-authorized-permission/edit-permission?merchantId=${
      merchant.id
    }&merchantName=${encodeURIComponent(merchant.merchant_name)}`,
  })
}

// 返回功能
const handleBack = () => {
  console.log('返回功能')
  uni.navigateBack()
}

onShow(() => {
  console.log('商户授权权限管理页面显示')
  fetchMerchants()
  fetchCategoryOptions()
})
</script>

<style lang="scss" scoped>
.merchant-permission-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

// 标题文字设为白色
:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

// 自定义返回按钮样式
:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  background-color: transparent;
  overflow: hidden;
  position: relative;
}

.main-layout {
  flex: 1;
  background-color: white;
  overflow-y: auto;
  padding: 20px;
}

.merchant-permission-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.action-bar {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
  padding: 0 0 15px 0;
  border-bottom: 1px solid #eee;
}

.search-bar {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.search-row {
  display: flex;
  gap: 20px;
  align-items: flex-end;
}

.search-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1px;
}

.search-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
  min-width: 60px;
}

.search-input-component {
  width: 120px;
  height: 28px;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  background-color: white !important;
  overflow: hidden;
}

:deep(.search-input-component .wd-input) {
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

:deep(.search-input-component .wd-input__inner) {
  padding: 6px 10px;
  font-size: 12px;
  height: 28px;
  box-sizing: border-box;
  border: none;
  background-color: transparent;
}

:deep(.search-input-component .wd-input:focus-within) {
  border-color: #4285f4;
}

:deep(.search-input-component .wd-input__inner:focus) {
  outline: none;
}

.button-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.right-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.merchant-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

// 表格行样式
:deep(.wd-table .wd-table__body .wd-table__row) {
  transition: background-color 0.2s;
}

.pagination-wrapper {
  padding: 15px 0;
}

// 分类标签
.category-tag {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;
}

// 地址文本
.address-text {
  font-size: 12px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 180px;
}

// 状态标签
.status-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;

  &.status-normal {
    background-color: #e8f5e8;
    color: #4caf50;
  }

  &.status-temporary {
    background-color: #fff3e0;
    color: #ff9800;
  }

  &.status-permanent {
    background-color: #ffebee;
    color: #f44336;
  }
}

// 商户名称文本
.merchant-name-text {
  font-size: 14px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100px;
  display: block;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-text {
  color: #999;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.loading-state {
  text-align: center;
  padding: 40px 20px;
}

.loading-text {
  color: #666;
  font-size: 14px;
}
</style>
