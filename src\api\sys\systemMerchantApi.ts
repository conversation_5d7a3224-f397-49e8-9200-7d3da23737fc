import { http } from '@/utils/http'
import type {
  ISysMerchantPageRequest,
  ISysMerchantListResponse,
  ISysMerchantDetailResponse,
  ISysMerchantCreateRequest,
  ISysMerchantUpdateRequest,
  ISysMerchantBatchDeleteRequest,
  ISysMerchantStatusRequest,
  ISysMerchantCommissionRequest,
  ISysMerchantBasicResponse,
  ISysMerchantSelectItem,
  ISysMerchantStatsResponse,
  ISysMerchantLocationResponse,
} from './types'
import { ISysMerchantListRequest } from './types'

/**
 * 分页查询商户列表
 * @param params 查询参数
 * @returns 返回分页商户列表
 */
export const getSystemMerchantsApi = (params?: ISysMerchantPageRequest) => {
  return http.get<IPageData<ISysMerchantListResponse>>('/business/merchants', params)
}

/**
 * 创建新商户
 * @param createRequest 创建请求参数
 * @returns 返回创建结果
 */
export const createSystemMerchantApi = (createRequest: ISysMerchantCreateRequest) => {
  return http.post<string>('/business/merchants', createRequest)
}

/**
 * 获取商户基础信息列表
 * @returns 返回商户基础信息列表
 */
export const getSystemMerchantBasicListApi = (params?: ISysMerchantListRequest) => {
  return http.get<ISysMerchantBasicResponse[]>('/business/merchants/basic', params)
}

/**
 * 批量删除商户
 * @param batchDeleteRequest 批量删除请求
 * @returns 返回批量删除结果
 */
export const batchDeleteSystemMerchantsApi = (
  batchDeleteRequest: ISysMerchantBatchDeleteRequest,
) => {
  return http.post<string>('/business/merchants/batch-delete', batchDeleteRequest)
}

/**
 * 获取商户选择项
 * @returns 返回用于下拉选择的商户列表
 */
export const getSystemMerchantSelectItemsApi = () => {
  return http.get<ISysMerchantSelectItem[]>('/business/merchants/select')
}

/**
 * 获取商户统计信息
 * @returns 返回商户统计数据
 */
export const getSystemMerchantStatsApi = () => {
  return http.get<ISysMerchantStatsResponse>('/business/merchants/stats')
}

/**
 * 查询商户详情
 * @param merchantId 商户ID
 * @returns 返回商户详情信息
 */
export const getSystemMerchantDetailApi = (merchantId: number) => {
  return http.get<ISysMerchantDetailResponse>(`/business/merchants/${merchantId}`)
}

/**
 * 更新商户信息
 * @param merchantId 商户ID
 * @param updateRequest 更新请求参数
 * @returns 返回更新结果
 */
export const updateSystemMerchantApi = (
  merchantId: number,
  updateRequest: ISysMerchantUpdateRequest,
) => {
  return http.put<string>(`/business/merchants/${merchantId}`, updateRequest)
}

/**
 * 删除商户
 * @param merchantId 商户ID
 * @returns 返回删除结果
 */
export const deleteSystemMerchantApi = (merchantId: number) => {
  return http.delete<string>(`/business/merchants/${merchantId}`)
}

/**
 * 设置商户佣金
 * @param merchantId 商户ID
 * @param commissionRequest 佣金设置请求
 * @returns 返回设置结果
 */
export const setSystemMerchantCommissionApi = (
  merchantId: number,
  commissionRequest: ISysMerchantCommissionRequest,
) => {
  return http.put<string>(`/business/merchants/${merchantId}/commission`, commissionRequest)
}

/**
 * 获取商户地理位置信息
 * @param merchantId 商户ID
 * @returns 返回商户地理位置信息
 */
export const getSystemMerchantLocationApi = (merchantId: number) => {
  return http.get<ISysMerchantLocationResponse>(`/business/merchants/${merchantId}/location`)
}

/**
 * 切换商户状态
 * @param merchantId 商户ID
 * @param statusRequest 状态切换请求
 * @returns 返回切换结果
 */
export const changeSystemMerchantStatusApi = (
  merchantId: number,
  statusRequest: ISysMerchantStatusRequest,
) => {
  return http.put<string>(`/business/merchants/${merchantId}/status`, statusRequest)
}

// 同时导出接口类型，方便外部使用
export type {
  ISysMerchantPageRequest,
  ISysMerchantListResponse,
  ISysMerchantDetailResponse,
  ISysMerchantCreateRequest,
  ISysMerchantUpdateRequest,
  ISysMerchantBatchDeleteRequest,
  ISysMerchantStatusRequest,
  ISysMerchantCommissionRequest,
  ISysMerchantBasicResponse,
  ISysMerchantSelectItem,
  ISysMerchantStatsResponse,
  ISysMerchantLocationResponse,
}
