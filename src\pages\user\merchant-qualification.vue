<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '商户资质',
  },
  access: {
    requireAuth: false,
  },
}
</route>

<template>
  <view class="qualification-page">
    <!-- 自定义导航栏 -->
    <Navbar title="商户资质" :fixed="true" :placeholder="true" />

    <!-- 标签页内容 -->
    <view class="tab-container">
      <wd-tabs v-model="activeTab" @change="handleTabChange">
        <wd-tab name="license" title="营业执照">
          <view class="license-content">
            <view class="section-title">营业执照信息</view>

            <!-- 营业执照图片 -->
            <view class="license-image-container">
              <image
                :src="licenseInfo.imageUrl"
                mode="widthFix"
                class="license-image"
                @click="previewImage(licenseInfo.imageUrl)"
              />
            </view>

            <!-- 营业执照信息 -->
            <view class="info-card">
              <view class="info-item">
                <text class="info-label">统一社会信用代码</text>
                <text class="info-value">{{ licenseInfo.creditCode }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">企业名称</text>
                <text class="info-value">{{ licenseInfo.companyName }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">法定代表人</text>
                <text class="info-value">{{ licenseInfo.legalPerson }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">注册资本</text>
                <text class="info-value">{{ licenseInfo.registeredCapital }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">成立日期</text>
                <text class="info-value">{{ licenseInfo.establishDate }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">营业期限</text>
                <text class="info-value">{{ licenseInfo.businessTerm }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">经营范围</text>
                <text class="info-value">{{ licenseInfo.businessScope }}</text>
              </view>
            </view>
          </view>
        </wd-tab>

        <wd-tab name="safety" title="安全档案">
          <view class="safety-content">
            <view class="section-title">安全认证信息</view>

            <!-- 认证列表 -->
            <view class="certification-list">
              <view
                v-for="(cert, index) in certifications"
                :key="index"
                class="certification-item"
              >
                <view class="cert-content">
                  <!-- 左侧图片 -->
                  <view class="cert-image-wrapper">
                    <image
                      v-if="cert.imageUrl"
                      :src="cert.imageUrl"
                      mode="aspectFill"
                      class="cert-image"
                      @click="previewImage(cert.imageUrl)"
                    />
                    <view v-else class="cert-image-placeholder">
                      <wd-icon name="picture" size="24px" color="#ccc" />
                    </view>
                  </view>

                  <!-- 右侧信息 -->
                  <view class="cert-info-wrapper">
                    <view class="cert-header">
                      <wd-icon name="check-bold" size="16px" color="#52c41a" />
                      <text class="cert-title">{{ cert.title }}</text>
                    </view>
                    <text class="cert-desc">{{ cert.description }}</text>
                    <text class="cert-date">有效期至：{{ cert.expiryDate }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </wd-tab>
      </wd-tabs>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useGlobalSafeArea } from '../../hooks/useSafeArea'

// 使用全局安全区域 hook
const { safeAreaInsetsTop } = useGlobalSafeArea()

// 计算导航栏高度
const navbarHeight = computed(() => {
  return safeAreaInsetsTop.value + 44
})

// 当前激活的标签页
const activeTab = ref('license')

// 营业执照信息
const licenseInfo = ref({
  imageUrl: 'https://images.unsplash.com/photo-**********-6726b3ff858f?w=800&h=600&fit=crop',
  creditCode: '91110105MA01K8CX5P',
  companyName: '北京阳光果园商贸有限公司',
  legalPerson: '张三',
  registeredCapital: '500万元人民币',
  establishDate: '2018年03月15日',
  businessTerm: '2018年03月15日 至 2048年03月14日',
  businessScope: '销售食品；销售新鲜水果、新鲜蔬菜；货物进出口；技术进出口；代理进出口。',
})

// 安全认证信息
const certifications = ref([
  {
    title: '食品经营许可证',
    description: '许可证编号：JY11105050123456',
    expiryDate: '2025年12月31日',
    imageUrl: 'https://images.unsplash.com/photo-1517048676732-d65bc937f952?w=800&h=600&fit=crop',
  },
  {
    title: '质量管理体系认证',
    description: 'ISO 9001:2015 质量管理体系认证',
    expiryDate: '2024年06月30日',
    imageUrl: 'https://images.unsplash.com/photo-**********-efe14ef6055d?w=800&h=600&fit=crop',
  },
  {
    title: '食品安全管理体系认证',
    description: 'ISO 22000:2018 食品安全管理体系认证',
    expiryDate: '2024年08月15日',
    imageUrl: '',
  },
])

// 标签页切换
const handleTabChange = ({ name }: { name: string }) => {
  console.log('切换到标签页:', name)
}

// 预览图片
const previewImage = (url: string) => {
  if (!url) return

  uni.previewImage({
    urls: [url],
    current: url,
  })
}
</script>

<style lang="scss" scoped>
.qualification-page {
  min-height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.tab-container {
  flex: 1;
  overflow: hidden;

  :deep(.wd-tabs) {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;

    .wd-tabs__nav {
      background-color: #fff;
      border-bottom: 1px solid #eee;
      flex-shrink: 0;
    }

    .wd-tabs__content {
      flex: 1;
      overflow-y: auto;
      background-color: #fff;
    }

    .wd-tab__panel {
      min-height: 100%;
    }
  }
}

// 营业执照内容
.license-content {
  padding: 16px;
  padding-bottom: 32px;
  min-height: calc(100vh - 200px); // 减去导航栏和标签栏的高度
  box-sizing: border-box;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.license-image-container {
  padding: 0;
  margin-bottom: 16px;
}

.license-image {
  width: 100%;
  border-radius: 8px;
}

.info-card {
  padding: 0;
}

.info-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  font-size: 14px;
  color: #666;
  width: 120px;
  flex-shrink: 0;
}

.info-value {
  font-size: 14px;
  color: #333;
  flex: 1;
  line-height: 1.5;
}

// 安全档案内容
.safety-content {
  padding: 16px;
  padding-bottom: 40px;
  min-height: calc(100vh - 200px); // 减去导航栏和标签栏的高度
  box-sizing: border-box;
}

.certification-list {
  display: flex;
  flex-direction: column;
}

.certification-item {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.cert-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.cert-image-wrapper {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
  background-color: #f5f5f5;
}

.cert-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s;

  &:active {
    transform: scale(0.95);
  }
}

.cert-image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.cert-info-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.cert-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
}

.cert-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  line-height: 1.2;
}

.cert-desc {
  font-size: 13px;
  color: #666;
  display: block;
  line-height: 1.4;
}

.cert-date {
  font-size: 12px;
  color: #999;
  display: block;
  margin-top: 2px;
}
</style>
