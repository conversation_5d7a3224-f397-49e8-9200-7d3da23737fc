<template>
  <view class="login-tabs">
    <view 
      v-for="tab in tabs"
      :key="tab.value"
      class="login-tab" 
      :class="{ active: modelValue === tab.value }"
      @click="$emit('update:modelValue', tab.value)"
    >
      {{ tab.label }}
    </view>
  </view>
</template>

<script lang="ts" setup>
interface Tab {
  label: string
  value: string
}

interface Props {
  modelValue: string
  tabs: Tab[]
}

defineProps<Props>()
defineEmits(['update:modelValue'])
</script>

<style lang="scss" scoped>
.login-tabs {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-bottom: 32px;
}

.login-tab {
  font-size: 16px;
  color: #999;
  padding-bottom: 8px;
  position: relative;
  transition: all 0.3s ease;

  &.active {
    color: #333;
    font-weight: 600;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 24px;
      height: 3px;
      background: #ff4757;
      border-radius: 2px;
    }
  }
}
</style> 