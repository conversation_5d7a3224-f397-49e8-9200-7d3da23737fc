import { http } from '@/utils/http'
import type {
  ISysPermissionTemplatePageRequest,
  ISystemPermissionTemplateListResponse,
  ISystemPermissionTemplateDetailResponse,
  ISysPermissionTemplateCreateRequest,
  ISysPermissionTemplateUpdateRequest,
  ISysPermissionTemplateBatchDeleteRequest,
  ISysPermissionTemplateStatusRequest,
  ISystemPermissionTemplateSelectItem,
  ISystemPermissionTemplateStatsResponse,
  ISystemPermissionTemplateTreeNode,
} from './types'

/**
 * 分页查询权限模板列表
 * @param params 查询参数
 * @returns 返回分页列表
 */
export const getSystemPermissionTemplatesApi = (params: ISysPermissionTemplatePageRequest) => {
  return http.get<IPageData<ISystemPermissionTemplateListResponse>>(
    '/system/permission-templates',
    params,
  )
}

/**
 * 创建权限模板
 * @param createRequest 创建请求
 * @returns 返回创建结果
 */
export const createSystemPermissionTemplateApi = (
  createRequest: ISysPermissionTemplateCreateRequest,
) => {
  return http.post<string>('/system/permission-templates', createRequest)
}

/**
 * 批量删除权限模板
 * @param batchDeleteRequest 批量删除请求
 * @returns 返回删除结果
 */
export const batchDeleteSystemPermissionTemplatesApi = (data: ISysPermissionTemplateBatchDeleteRequest) => {
  return http.post<string>('/system/permission-templates/batch-delete', data)
}

/**
 * 获取权限模板选择项
 * @returns 返回选择项列表
 */
export const getSystemPermissionTemplateSelectItemsApi = () => {
  return http.get<ISystemPermissionTemplateSelectItem[]>('/system/permission-templates/select')
}

/**
 * 获取权限模板统计信息
 * @returns 返回统计信息
 */
export const getSystemPermissionTemplateStatsApi = () => {
  return http.get<ISystemPermissionTemplateStatsResponse>('/system/permission-templates/stats')
}

/**
 * 获取权限模板树形结构
 * @returns 返回树形结构
 */
export const getSystemPermissionTemplateTreeApi = () => {
  return http.get<ISystemPermissionTemplateTreeNode[]>('/system/permission-templates/tree')
}

/**
 * 查询权限模板详情
 * @param templateId 模板ID
 * @returns 返回详情
 */
export const getSystemPermissionTemplateDetailApi = (templateId: string) => {
  return http.get<ISystemPermissionTemplateDetailResponse>(
    `/system/permission-templates/${templateId}`,
  )
}

/**
 * 更新权限模板
 * @param templateId 模板ID
 * @param updateRequest 更新请求
 * @returns 返回更新结果
 */
export const updateSystemPermissionTemplateApi = (
  templateId: string,
  updateRequest: ISysPermissionTemplateUpdateRequest,
) => {
  return http.put<string>(`/system/permission-templates/${templateId}`, updateRequest)
}

/**
 * 删除权限模板
 * @param templateId 模板ID
 * @returns 返回删除结果
 */
export const deleteSystemPermissionTemplateApi = (id: string) => {
  return http.delete<string>(`/system/permission-templates/${id}`)
}

/**
 * 切换权限模板状态
 * @param templateId 模板ID
 * @param statusRequest 状态请求
 * @returns 返回切换结果
 */
export const changeSystemPermissionTemplateStatusApi = (
  templateId: string,
  statusRequest: ISysPermissionTemplateStatusRequest,
) => {
  return http.put<string>(`/system/permission-templates/${templateId}/status`, statusRequest)
}

// 同时导出接口类型，方便外部使用
export type {
  ISysPermissionTemplatePageRequest,
  ISystemPermissionTemplateListResponse,
  ISystemPermissionTemplateDetailResponse,
  ISysPermissionTemplateCreateRequest,
  ISysPermissionTemplateUpdateRequest,
  ISysPermissionTemplateBatchDeleteRequest,
  ISysPermissionTemplateStatusRequest,
  ISystemPermissionTemplateSelectItem,
  ISystemPermissionTemplateStatsResponse,
  ISystemPermissionTemplateTreeNode,
} 