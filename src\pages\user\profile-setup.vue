<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '完善信息',
  },
  access: {
    requireAuth: false,
  },
}
</route>


<template>
  <view class="profile-setup-container">
    <!-- 返回按钮 -->
    <view class="back-button" @click="goBack">
      <text class="iconfont-sys iconsys-fanhui"></text>
    </view>

    <view class="setup-header">
      <text class="setup-title">完善个人信息</text>
      <text class="setup-subtitle">请设置您的头像和昵称</text>
    </view>

    <view class="setup-form">
      <!-- 头像选择 -->
      <view class="avatar-section">
        <!-- #ifdef MP-WEIXIN -->
        <button open-type="chooseAvatar" class="avatar-button" @chooseavatar="onChooseAvatar">
          <image :src="userAvatar" class="avatar-preview" mode="aspectFill"></image>
          <view class="avatar-edit-hint">
            <text class="iconfont-sys iconsys-xiangji"></text>
          </view>
        </button>
        <!-- #endif -->

        <!-- #ifdef APP-PLUS || H5 -->
        <view class="avatar-wrapper" @click="onChooseImage">
          <image :src="userAvatar" class="avatar-preview" mode="aspectFill"></image>
          <view class="avatar-edit-hint">
            <text class="iconfont-sys iconsys-xiangji"></text>
          </view>
        </view>
        <!-- #endif -->
      </view>

      <!-- 昵称输入 -->
      <view class="nickname-section">
        <text class="input-label">昵称</text>
        <input
          type="nickname"
          class="nickname-input"
          placeholder="请输入昵称"
          placeholder-class="input-placeholder"
          v-model="nickname"
          @change="onNicknameChange"
        />
      </view>

      <!-- 提交按钮 -->
      <button
        class="submit-button"
        :disabled="!isFormValid"
        :loading="isSubmitting"
        @click="handleSubmit"
      >
        {{ isSubmitting ? '提交中...' : '确认' }}
      </button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import WxUtil from '../../utils/wxUtil'
import PLATFORM from '../../utils/platform'

// 用户信息
const nickname = ref('')
const userAvatar = ref(WxUtil.getDefaultAvatarUrl())
const isSubmitting = ref(false)

// 表单验证
const isFormValid = computed(() => {
  return nickname.value.trim().length > 0 && !WxUtil.isDefaultAvatar(userAvatar.value)
})

// 返回按钮处理
const goBack = () => {
  uni.navigateBack({
    fail: () => {
      uni.redirectTo({
        url: '/pages/user/index',
      })
    },
  })
}

// 头像选择处理 - 微信小程序
const onChooseAvatar = (e: any) => {
  const avatarUrl = WxUtil.handleChooseAvatar(e)
  userAvatar.value = avatarUrl
}

// 头像选择处理 - APP端
const onChooseImage = async () => {
  try {
    const res = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
    })

    if (res && res.tempFilePaths && res.tempFilePaths.length > 0) {
      userAvatar.value = res.tempFilePaths[0]
    }
  } catch (error) {
    console.error('选择图片失败:', error)
    uni.showToast({
      title: '选择图片失败',
      icon: 'none',
    })
  }
}

// 昵称输入处理
const onNicknameChange = (e: any) => {
  nickname.value = WxUtil.formatNickname(e.detail.value)
}

// 提交表单
const handleSubmit = async () => {
  if (!isFormValid.value) {
    uni.showToast({
      title: '请完善头像和昵称',
      icon: 'none',
    })
    return
  }

  isSubmitting.value = true

  try {
    // 上传头像到服务器
    // 实际项目中，这里应该调用后端接口上传头像
    const avatarUrl = await WxUtil.uploadAvatar(userAvatar.value)

    // 创建用户信息对象
    const userInfo = WxUtil.createUserInfo(nickname.value, avatarUrl)

    // 不再保存用户信息到本地存储
    // 而是通过页面参数将用户信息返回给调用页面

    uni.showToast({
      title: '信息提交成功',
      icon: 'success',
    })

    // 延迟跳转
    setTimeout(() => {
      // 获取页面参数
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const options = (currentPage as any).options

      if (options && options.redirect) {
        // 将用户信息作为参数返回
        const redirectUrl = decodeURIComponent(options.redirect)
        const userInfoParam = encodeURIComponent(JSON.stringify(userInfo))

        uni.redirectTo({
          url: `${redirectUrl}?userInfo=${userInfoParam}`,
        })
      } else {
        // 默认跳转到用户页面，同样携带用户信息
        const userInfoParam = encodeURIComponent(JSON.stringify(userInfo))
        uni.redirectTo({
          url: `/pages/user/index?userInfo=${userInfoParam}`,
        })
      }
    }, 1500)
  } catch (error: any) {
    console.error('提交用户信息失败:', error)
    uni.showToast({
      title: error.message || '提交失败，请重试',
      icon: 'none',
    })
  } finally {
    isSubmitting.value = false
  }
}

// 页面初始化
onMounted(() => {
  // 检查平台环境
  const systemInfo = uni.getSystemInfoSync()
  console.log('当前平台:', systemInfo.platform)

  // 使用platform.ts检查环境
  if (PLATFORM.isMpWeixin) {
    console.log('当前在微信小程序环境')
    // 检查微信环境是否支持头像昵称填写能力
    if (!WxUtil.isSupportAvatarNickname()) {
      uni.showModal({
        title: '提示',
        content: '当前微信版本过低，请升级后重试',
        showCancel: false,
      })
    }
  } else if (PLATFORM.isApp) {
    console.log('当前在APP环境')
  } else if (PLATFORM.isH5) {
    console.log('当前在H5环境')
  }
})
</script>

<style lang="scss" scoped>
.profile-setup-container {
  min-height: 100vh;
  background-color: #ffffff;
  padding: 32px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.back-button {
  position: absolute;
  top: 32px;
  left: 20px;
  z-index: 10;
  padding: 10px;
}

.back-button .iconfont-sys {
  font-size: 24px;
  color: #333;
}

.setup-header {
  margin-top: 30px;
  margin-bottom: 48px;
  text-align: center;
}

.setup-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.setup-subtitle {
  font-size: 14px;
  color: #666;
  display: block;
}

.setup-form {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16px;
}

.avatar-preview {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
}

.avatar-button {
  background-color: transparent;
  padding: 0;
  margin: 0;
  border: none;
  position: relative;
  line-height: 1;
}

.avatar-button::after {
  border: none;
}

.avatar-wrapper {
  position: relative;
}

.avatar-edit-hint {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-edit-hint .iconfont-sys {
  font-size: 18px;
  color: #ffffff;
}

.nickname-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.nickname-input {
  height: 48px;
  border-bottom: 1px solid #e0e0e0;
  font-size: 16px;
  padding: 0 8px;
}

.input-placeholder {
  color: #999;
}

.submit-button {
  margin-top: 32px;
  height: 48px;
  background-color: #ff4757;
  color: white;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:disabled {
    background-color: #cccccc;
  }
}
</style>
