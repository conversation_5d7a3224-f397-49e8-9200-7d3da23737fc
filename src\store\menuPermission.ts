import { defineStore } from 'pinia'
import { computed, ref, watch } from 'vue'
import type { IRouterVo } from '@/api/sys/types/permission'
import { getUserRoutersApi } from '@/api/sys/systemPermissionApi'
import { useSystemAdminStore } from './systemAdmin'

/**
 * 菜单类型枚举
 */
export enum MenuTypeEnum {
  /** 目录 */
  DIRECTORY = 1,
  /** 菜单 */
  MENU = 2,
  /** 按钮 */
  BUTTON = 3,
}

/**
 * TabBar项接口
 */
export interface ITabBarItem {
  name: string
  path: string
  title: string
  icon: string
  badge?: string
}

/**
 * 侧边栏菜单项接口
 */
export interface ISidebarMenuItem {
  id: string
  name: string
  path: string
  title: string
  icon?: string
  hidden?: boolean
  perms?: string
  component?: string
  children?: ISidebarMenuItem[]
}

export const useMenuPermissionStore = defineStore(
  'menuPermission',
  () => {
    // ========================================
    // 状态定义
    // ========================================

    /** 原始菜单数据 */
    const rawMenus = ref<IRouterVo[]>([])

    /** 当前激活的TabBar */
    const activeTabBar = ref<string>('')

    /** 菜单加载状态 */
    const menuLoading = ref<boolean>(false)

    // ========================================
    // 计算属性 (Getters)
    // ========================================

    /**
     * TabBar列表 - 从原始菜单数据计算得出
     */
    const tabBarList = computed<ITabBarItem[]>(() => {
      return rawMenus.value
        .filter((menu) => menu.menu_type === MenuTypeEnum.DIRECTORY && !menu.hidden)
        .map((menu) => ({
          name: menu.name || menu.path.replace('/', ''),
          path: menu.path,
          title: menu.meta.title,
          icon: menu.meta.icon || 'home',
        }))
    })

    /**
     * 获取指定TabBar的侧边栏菜单
     */
    const getSidebarMenus = computed(() => {
      return (tabBarPath: string): ISidebarMenuItem[] => {
        const targetMenu = rawMenus.value.find((menu) => menu.path === tabBarPath)
        if (!targetMenu || !targetMenu.children) return []

        return buildSidebarMenus(targetMenu.children)
      }
    })

    /**
     * 当前激活TabBar的侧边栏菜单
     */
    const currentSidebarMenus = computed<ISidebarMenuItem[]>(() => {
      return getSidebarMenus.value(activeTabBar.value)
    })

    /**
     * 扁平化的所有菜单路径列表（用于路由判断）
     */
    const flatMenuPaths = computed<string[]>(() => {
      const paths: string[] = []

      const collectPaths = (menus: IRouterVo[], parentPath = '') => {
        menus.forEach((menu) => {
          const fullPath = parentPath + menu.path
          paths.push(fullPath)

          if (menu.children && menu.children.length > 0) {
            collectPaths(menu.children, fullPath + '/')
          }
        })
      }

      collectPaths(rawMenus.value)
      return paths
    })

    // ========================================
    // 工具函数
    // ========================================

    /**
     * 清理所有菜单相关数据
     */
    const clearAllMenuData = (): void => {
      rawMenus.value = []
    }

    /**
     * 构建侧边栏菜单数据
     */
    const buildSidebarMenus = (menus: IRouterVo[]): ISidebarMenuItem[] => {
      return menus.map((menu) => ({
        id: menu.name || menu.path,
        name: menu.name || menu.path,
        path: menu.path,
        title: menu.meta.title,
        icon: menu.meta.icon,
        hidden: menu.hidden,
        perms: menu.perms,
        component: menu.component,
        children:
          menu.children && menu.children.length > 0 ? buildSidebarMenus(menu.children) : undefined,
      }))
    }

    /**
     * 检查是否有菜单权限
     */
    const hasMenuPermission = (menu: IRouterVo): boolean => {
      // 如果菜单没有权限要求，直接允许访问
      if (!menu.perms) return true

      // 从systemAdmin store获取权限数据
      const systemAdminStore = useSystemAdminStore()
      return systemAdminStore.hasPermission(menu.perms)
    }

    // ========================================
    // Actions
    // ========================================

    /**
     * 获取用户菜单数据
     */
    const fetchUserMenus = async (): Promise<void> => {
      try {
        menuLoading.value = true
        const response = await getUserRoutersApi()

        if (response.code === 200) {
          rawMenus.value = response.data || []
          console.log('rawMenus', response)
        } else {
          console.error('获取菜单失败:', response.message)
          clearAllMenuData()
        }
      } catch (error) {
        console.error('获取菜单异常:', error)
        clearAllMenuData()
      } finally {
        menuLoading.value = false
      }
    }

    /**
     * 设置激活的TabBar
     */
    const setActiveTabBar = (tabBarPath: string): void => {
      activeTabBar.value = tabBarPath
    }

    /**
     * 清空菜单数据
     */
    const clearMenus = (): void => {
      activeTabBar.value = ''
      clearAllMenuData()
    }

    /**
     * 重新加载菜单
     */
    const reloadMenus = async (): Promise<void> => {
      clearMenus()
      await fetchUserMenus()
    }

    // ========================================
    // 返回store接口
    // ========================================

    return {
      // 状态
      rawMenus: rawMenus,
      activeTabBar: activeTabBar,
      menuLoading: menuLoading,
      tabBarList: tabBarList,

      // 计算属性
      currentSidebarMenus,
      getSidebarMenus,
      flatMenuPaths,

      // 方法
      fetchUserMenus,
      setActiveTabBar,
      clearMenus,
      reloadMenus,
      hasMenuPermission,
    }
  },
  { persist: true },
)

export type MenuPermissionStore = ReturnType<typeof useMenuPermissionStore>
