/**
 * 微信小程序工具类
 * 封装微信相关API，包括登录、头像昵称填写等功能
 */

// 微信登录结果接口
export interface WxLoginResult {
  code: string
  errMsg: string
}

// 用户信息接口
export interface UserInfo {
  nickname: string
  avatar: string
  openid?: string
}

// 头像选择事件详情
export interface ChooseAvatarDetail {
  avatarUrl: string
}

// 微信登录选项
export interface WxLoginOptions {
  provider?: 'weixin' | 'qq' | 'sinaweibo' | 'xiaomi' | 'apple' | 'univerify'
  onlyAuthorize?: boolean
  timeout?: number
}

export default class WxUtil {
  // 默认头像URL
  private static readonly DEFAULT_AVATAR_URL =
    'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'

  /**
   * 微信登录 - 获取code
   * @param options 登录选项
   * @returns Promise<WxLoginResult>
   */
  static async login(options: WxLoginOptions = {}): Promise<WxLoginResult> {
    const { provider = 'weixin' as const, onlyAuthorize = true, timeout = 10000 } = options

    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('微信登录超时'))
      }, timeout)

      uni.login({
        provider,
        onlyAuthorize,
        success: (res) => {
          clearTimeout(timer)
          console.log('微信登录授权成功:', res)
          resolve(res as WxLoginResult)
        },
        fail: (err) => {
          clearTimeout(timer)
          console.error('微信登录授权失败:', err)
          reject(err)
        },
      })
    })
  }

  /**
   * 处理头像选择事件
   * @param event 头像选择事件
   * @returns 选择的头像URL
   */
  static handleChooseAvatar(event: any): string {
    const { avatarUrl } = event.detail as ChooseAvatarDetail
    console.log('用户选择头像:', avatarUrl)
    return avatarUrl
  }

  /**
   * 获取默认头像URL
   * @returns 默认头像URL
   */
  static getDefaultAvatarUrl(): string {
    return this.DEFAULT_AVATAR_URL
  }

  /**
   * 检查是否使用默认头像
   * @param avatarUrl 头像URL
   * @returns 是否为默认头像
   */
  static isDefaultAvatar(avatarUrl: string): boolean {
    return avatarUrl === this.DEFAULT_AVATAR_URL || !avatarUrl
  }

  /**
   * 上传头像到服务器（需要实现具体上传逻辑）
   * @param tempFilePath 临时文件路径
   * @returns Promise<string> 服务器头像URL
   */
  static async uploadAvatar(tempFilePath: string): Promise<string> {
    try {
      // TODO: 实现具体的上传逻辑
      console.log('上传头像:', tempFilePath)

      // 临时返回原路径，实际应该上传到服务器
      return tempFilePath

      /*
      // 示例上传代码
      const uploadResult = await uni.uploadFile({
        url: 'https://your-api-domain.com/api/upload/avatar',
        filePath: tempFilePath,
        name: 'avatar',
        header: {
          'Authorization': 'Bearer YOUR_TOKEN_HERE'
        }
      })

      const response = JSON.parse(uploadResult.data)
      if (response.success) {
        return response.data.url
      } else {
        throw new Error(response.message || '上传失败')
      }
      */
    } catch (error) {
      console.error('上传头像失败:', error)
      throw error
    }
  }

  /**
   * 检查微信版本是否支持头像昵称填写
   * @returns 是否支持
   */
  static isSupportAvatarNickname(): boolean {
    try {
      const systemInfo = uni.getSystemInfoSync()
      // 基础库 2.21.2 开始支持
      const SDKVersion = systemInfo.SDKVersion || '0.0.0'
      const [major, minor, patch] = SDKVersion.split('.').map(Number)

      if (major > 2) return true
      if (major === 2 && minor > 21) return true
      if (major === 2 && minor === 21 && patch >= 2) return true

      return false
    } catch (error) {
      console.warn('检查微信版本失败:', error)
      return false
    }
  }

  /**
   * 格式化用户昵称（移除特殊字符等）
   * @param nickname 原始昵称
   * @returns 格式化后的昵称
   */
  static formatNickname(nickname: string): string {
    if (!nickname) return ''

    // 移除首尾空格
    let formatted = nickname.trim()

    // 限制长度（一般昵称不超过20个字符）
    if (formatted.length > 20) {
      formatted = formatted.substring(0, 20)
    }

    return formatted
  }

  /**
   * 创建用户信息对象
   * @param nickname 昵称
   * @param avatar 头像URL
   * @param openid OpenID（可选）
   * @returns 用户信息对象
   */
  static createUserInfo(nickname: string, avatar: string, openid?: string): UserInfo {
    return {
      nickname: this.formatNickname(nickname),
      avatar: avatar || this.DEFAULT_AVATAR_URL,
      openid,
    }
  }
}
