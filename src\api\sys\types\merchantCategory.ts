/**
 * 商户分类分页查询请求参数
 */
export interface ISysMerchantCategoryPageRequest {
  // 分类名称
  category_name?: string | null
  // 分类编码
  category_code?: string | null
  // 状态（1启用 2禁用）
  status?: number | null
  // 当前页码
  page?: number | null
  // 每页大小
  page_size?: number | null
  // 创建开始时间
  created_start?: string | null
  // 创建结束时间
  created_end?: string | null
}

/**
 * 商户分类列表响应VO
 */
export interface ISysMerchantCategoryListResponse {
  // 分类ID
  id: string
  // 分类名称
  category_name: string
  // 分类编码
  category_code?: string | null
  // 分类描述
  description?: string | null
  // 排序字段
  sort_order?: number | null
  // 状态（1启用 2禁用）
  status: number
  // 创建时间
  created_date: string
  // 更新时间
  updated_date: string
}

/**
 * 商户分类详情响应VO
 */
export interface ISysMerchantCategoryDetailResponse {
  // 分类ID
  id: string
  // 分类名称
  category_name: string
  // 分类编码
  category_code?: string | null
  // 分类描述
  description?: string | null
  // 排序字段
  sort_order?: number | null
  // 状态（1启用 2禁用）
  status: number
  // 创建时间
  created_date: string
  // 更新时间
  updated_date: string
  // 创建人ID
  created_by?: string | null
  // 更新人ID
  updated_by?: string | null
  // 备注
  remark?: string | null
}

/**
 * 商户分类创建请求DTO
 */
export interface ISysMerchantCategoryCreateRequest {
  // 分类名称
  category_name: string
  // 分类编码
  category_code?: string | null
  // 分类描述
  description?: string | null
  // 排序字段
  sort_order?: number | null
  // 状态（1启用 2禁用）
  status: number
  // 备注
  remark?: string | null
}

/**
 * 商户分类更新请求DTO
 */
export interface ISysMerchantCategoryUpdateRequest {
  // 分类名称
  category_name: string
  // 分类编码
  category_code?: string | null
  // 分类描述
  description?: string | null
  // 排序字段
  sort_order?: number | null
  // 状态（1启用 2禁用）
  status: number
  // 备注
  remark?: string | null
}

/**
 * 商户分类批量删除请求DTO
 */
export interface ISysMerchantCategoryBatchDeleteRequest {
  // 分类ID列表
  category_ids: string[]
}

/**
 * 商户分类状态切换请求DTO
 */
export interface ISysMerchantCategoryStatusRequest {
  // 状态（1启用 2禁用）
  status: number
}

/**
 * 商户分类选择项VO
 */
export interface ISysMerchantCategorySelectItem {
  // 分类ID
  id: string
  // 分类名称
  category_name: string
  // 分类编码
  category_code?: string | null
  // 排序字段
  sort_order?: number | null
}

/**
 * 商户分类统计信息响应VO
 */
export interface ISysMerchantCategoryStatsResponse {
  // 分类ID
  category_id: string
  // 分类名称
  category_name: string
  // 商户总数
  merchant_count: number
  // 活跃商户数
  active_merchant_count: number
  // 非活跃商户数
  inactive_merchant_count: number
} 